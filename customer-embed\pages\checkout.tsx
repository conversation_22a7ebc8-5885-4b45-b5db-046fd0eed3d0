import React, { useState, useEffect } from 'react';

interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName?: string;
  status: string;
  type: string;
  addresses?: Array<{
    street1: string;
    street2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  }>;
}

interface TaxCalculation {
  taxAmount: number;
  taxRate: number;
  totalAmount: number;
}

export default function Checkout() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [taxData, setTaxData] = useState<TaxCalculation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Sample checkout items
  const [items] = useState([
    { id: '1', name: 'Premium Service Package', price: 299.99, quantity: 1 },
    { id: '2', name: 'Additional Features', price: 49.99, quantity: 2 },
    { id: '3', name: 'Support Plan', price: 19.99, quantity: 1 },
  ]);

  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  useEffect(() => {
    fetchCustomers();
  }, []);

  useEffect(() => {
    if (selectedCustomer) {
      calculateTax();
    }
  }, [selectedCustomer]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL;
      const response = await fetch(`${apiUrl}/customers`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const customerData = await response.json();

      // Handle the response format from the backend API
      // The backend returns { data: Customer[], total: number, skip: number, take: number }
      if (customerData && Array.isArray(customerData.data)) {
        setCustomers(customerData.data);
      } else if (Array.isArray(customerData)) {
        // Fallback for direct array response
        setCustomers(customerData);
      } else {
        console.error('Unexpected response format:', customerData);
        setCustomers([]);
      }

      setError(null);
    } catch (err) {
      setError('Failed to load customer data');
      console.error('Error fetching customers:', err);
      setCustomers([]); // Ensure customers is always an array
    } finally {
      setLoading(false);
    }
  };

  const calculateTax = async () => {
    if (!selectedCustomer || !selectedCustomer.addresses?.length) return;

    try {
      const address = selectedCustomer.addresses[0];
      const apiUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL;
      const response = await fetch(`${apiUrl}/tax/calculate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: subtotal,
          address: {
            street: address.street1,
            city: address.city,
            state: address.state,
            zipCode: address.postalCode,
            country: address.country,
          },
        }),
      });
      const taxCalculation = await response.json();
      setTaxData(taxCalculation);
    } catch (err) {
      console.error('Error calculating tax:', err);
    }
  };

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
  };

  const handleCheckout = () => {
    if (!selectedCustomer || !taxData) {
      alert('Please select a customer and ensure tax calculation is complete');
      return;
    }

    // Simulate checkout process
    alert(`Checkout completed for ${selectedCustomer.firstName} ${selectedCustomer.lastName}!\nTotal: $${taxData.totalAmount.toFixed(2)}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading customer data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️ Error</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={fetchCustomers}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Checkout Demo - Customer Integration</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Customer Selection */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Select Customer</h2>
              <p className="text-gray-600 mb-4">Choose from seeded customer data:</p>

              {customers && customers.length > 0 ? (
                <div className="space-y-4">
                  <div>
                    <label htmlFor="customer-select" className="block text-sm font-medium text-gray-700 mb-2">
                      Customer
                    </label>
                    <select
                      id="customer-select"
                      value={selectedCustomer?.id || ''}
                      onChange={(e) => {
                        const customer = customers.find(c => c.id === e.target.value);
                        if (customer) handleCustomerSelect(customer);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select a customer...</option>
                      {customers.map((customer) => (
                        <option key={customer.id} value={customer.id}>
                          {customer.firstName} {customer.lastName} - {customer.email}
                        </option>
                      ))}
                    </select>
                  </div>

                  {selectedCustomer && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">Selected Customer</h4>
                      <div className="text-sm text-blue-800">
                        <p><strong>Name:</strong> {selectedCustomer.firstName} {selectedCustomer.lastName}</p>
                        <p><strong>Email:</strong> {selectedCustomer.email}</p>
                        <p><strong>Phone:</strong> {selectedCustomer.phone}</p>
                        {selectedCustomer.companyName && (
                          <p><strong>Company:</strong> {selectedCustomer.companyName}</p>
                        )}
                        <p><strong>Status:</strong> {selectedCustomer.status}</p>
                        {selectedCustomer.addresses && selectedCustomer.addresses.length > 0 && (
                          <p><strong>Address:</strong> {selectedCustomer.addresses[0].city}, {selectedCustomer.addresses[0].state}</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-400 text-lg mb-2">📋</div>
                  <p className="text-gray-600 mb-2">No customers available</p>
                  <p className="text-sm text-gray-500">
                    Make sure the customer service is running and has seeded data.
                  </p>
                  <button
                    onClick={fetchCustomers}
                    className="mt-3 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    Retry loading customers
                  </button>
                </div>
              )}
            </div>

            {/* Selected Customer Details */}
            {selectedCustomer && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Customer Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Personal Information</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>Name:</strong> {selectedCustomer.firstName} {selectedCustomer.lastName}</p>
                      <p><strong>Email:</strong> {selectedCustomer.email}</p>
                      <p><strong>Phone:</strong> {selectedCustomer.phone}</p>
                      <p><strong>Status:</strong> {selectedCustomer.status}</p>
                      <p><strong>Type:</strong> {selectedCustomer.type}</p>
                    </div>
                  </div>
                  
                  {selectedCustomer.addresses && selectedCustomer.addresses.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Billing Address</h4>
                      <div className="text-sm text-gray-600">
                        <p>{selectedCustomer.addresses[0].street1}</p>
                        {selectedCustomer.addresses[0].street2 && (
                          <p>{selectedCustomer.addresses[0].street2}</p>
                        )}
                        <p>
                          {selectedCustomer.addresses[0].city}, {selectedCustomer.addresses[0].state} {selectedCustomer.addresses[0].postalCode}
                        </p>
                        <p>{selectedCustomer.addresses[0].country}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
              
              <div className="space-y-3 mb-4">
                {items.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-gray-600">Qty: {item.quantity}</p>
                    </div>
                    <p className="font-medium">${(item.price * item.quantity).toFixed(2)}</p>
                  </div>
                ))}
              </div>
              
              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal:</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                
                {taxData && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span>Tax ({(taxData.taxRate * 100).toFixed(2)}%):</span>
                      <span>${taxData.taxAmount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-semibold text-lg border-t pt-2">
                      <span>Total:</span>
                      <span>${taxData.totalAmount.toFixed(2)}</span>
                    </div>
                  </>
                )}
                
                {!taxData && selectedCustomer && (
                  <div className="text-sm text-gray-500">Calculating tax...</div>
                )}
              </div>
              
              <button
                onClick={handleCheckout}
                disabled={!selectedCustomer || !taxData}
                className={`w-full mt-6 py-3 px-4 rounded-lg font-medium ${
                  selectedCustomer && taxData
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Complete Checkout
              </button>
            </div>

            {/* Integration Info */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">🔗 Integration Demo</h4>
              <p className="text-sm text-blue-800">
                This page demonstrates how the customer microservice integrates with a finance system. 
                Customer data is fetched from the seeded database and used for checkout processing.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
