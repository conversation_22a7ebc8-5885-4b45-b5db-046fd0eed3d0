# Test Environment Configuration
NODE_ENV=test

# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/customer_service_test"

# JWT Configuration
JWT_SECRET="test-jwt-secret-key-for-testing-only"
JWT_EXPIRES_IN="1h"

# External Auth Service
AUTH_SERVICE_URL="http://localhost:3001"
AUTH_SERVICE_ADMIN_EMAIL="<EMAIL>"
AUTH_SERVICE_ADMIN_PASSWORD="test-admin-password"
AUTH_SERVICE_USER_EMAIL="<EMAIL>"
AUTH_SERVICE_USER_PASSWORD="test-user-password"

# API Configuration
API_PORT=3001
API_PREFIX="api/v1"

# Logging
LOG_LEVEL="error"

# CORS
CORS_ORIGIN="http://localhost:3001"

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_DEST="./uploads"

# Redis (if needed for testing)
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# AWS S3 (mock for testing)
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="test-access-key"
AWS_SECRET_ACCESS_KEY="test-secret-key"
AWS_S3_BUCKET="test-bucket"
