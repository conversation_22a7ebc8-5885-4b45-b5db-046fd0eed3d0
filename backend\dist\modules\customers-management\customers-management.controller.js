"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CustomersManagementController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersManagementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const customers_management_service_1 = require("./customers-management.service");
const customer_management_dto_1 = require("./dto/customer-management.dto");
const customer_shared_dto_1 = require("../customers-shared/dto/customer-shared.dto");
let CustomersManagementController = CustomersManagementController_1 = class CustomersManagementController {
    customersManagementService;
    logger = new common_1.Logger(CustomersManagementController_1.name);
    constructor(customersManagementService) {
        this.customersManagementService = customersManagementService;
    }
    mapToResponseDto(customer) {
        return {
            ...customer,
            externalId: customer.externalId || undefined,
        };
    }
    async createCustomer(createCustomerDto) {
        this.logger.log(`Creating customer`);
        const customerData = {
            ...createCustomerDto,
            dateOfBirth: createCustomerDto.dateOfBirth ? new Date(createCustomerDto.dateOfBirth) : undefined,
        };
        const customer = await this.customersManagementService.create(customerData);
        return this.mapToResponseDto(customer);
    }
    async updateCustomer(id, updateCustomerDto) {
        this.logger.log(`Updating customer ${id}`);
        const customerData = {
            ...updateCustomerDto,
            dateOfBirth: updateCustomerDto.dateOfBirth ? new Date(updateCustomerDto.dateOfBirth) : undefined,
        };
        const customer = await this.customersManagementService.update(id, customerData);
        return this.mapToResponseDto(customer);
    }
    async deleteCustomer(id) {
        this.logger.log(`Deleting customer ${id}`);
        await this.customersManagementService.delete(id);
        return {
            success: true,
            message: 'Customer deleted successfully'
        };
    }
};
exports.CustomersManagementController = CustomersManagementController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new customer',
        description: 'Create a new customer record with the provided information. All required fields must be included in the request body.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Customer created successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
        examples: {
            success: {
                summary: 'Successful customer creation',
                value: {
                    id: 'cmca6q7w40000l9015jo4lc5o',
                    email: '<EMAIL>',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '+1234567890',
                    status: 'ACTIVE',
                    type: 'INDIVIDUAL',
                    isEmailVerified: false,
                    isKycVerified: false,
                    createdAt: '2024-01-15T10:30:00Z',
                    updatedAt: '2024-01-15T10:30:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or validation errors',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'array', items: { type: 'string' }, example: ['email must be a valid email', 'firstName should not be empty'] },
                error: { type: 'string', example: 'Bad Request' },
                statusCode: { type: 'number', example: 400 }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Customer with this email already exists',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [customer_management_dto_1.CreateCustomerDto]),
    __metadata("design:returntype", Promise)
], CustomersManagementController.prototype, "createCustomer", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update customer by ID',
        description: 'Update an existing customer record with new information. Only provided fields will be updated.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer updated successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
        examples: {
            success: {
                summary: 'Successful customer update',
                value: {
                    id: 'cmca6q7w40000l9015jo4lc5o',
                    email: '<EMAIL>',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '+1234567890',
                    status: 'ACTIVE',
                    type: 'INDIVIDUAL',
                    isEmailVerified: true,
                    isKycVerified: false,
                    createdAt: '2024-01-15T10:30:00Z',
                    updatedAt: '2024-01-15T14:45:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or validation errors',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, customer_management_dto_1.UpdateCustomerDto]),
    __metadata("design:returntype", Promise)
], CustomersManagementController.prototype, "updateCustomer", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete customer by ID (soft delete)',
        description: 'Perform a soft delete on the customer record. The customer will be marked as deleted but data will be retained for audit purposes.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer deleted successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Customer deleted successfully' }
            }
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to delete customer',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersManagementController.prototype, "deleteCustomer", null);
exports.CustomersManagementController = CustomersManagementController = CustomersManagementController_1 = __decorate([
    (0, swagger_1.ApiTags)('customers-management'),
    (0, common_1.Controller)('customers'),
    __metadata("design:paramtypes", [customers_management_service_1.CustomersManagementService])
], CustomersManagementController);
//# sourceMappingURL=customers-management.controller.js.map