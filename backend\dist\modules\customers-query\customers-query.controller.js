"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CustomersQueryController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersQueryController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const customers_query_service_1 = require("./customers-query.service");
const customer_shared_dto_1 = require("../customers-shared/dto/customer-shared.dto");
let CustomersQueryController = CustomersQueryController_1 = class CustomersQueryController {
    customersQueryService;
    logger = new common_1.Logger(CustomersQueryController_1.name);
    constructor(customersQueryService) {
        this.customersQueryService = customersQueryService;
    }
    mapToResponseDto(customer) {
        return {
            ...customer,
            externalId: customer.externalId || undefined,
        };
    }
    mapToResponseDtoArray(customers) {
        return customers.map(customer => this.mapToResponseDto(customer));
    }
    async getCustomerStatistics() {
        this.logger.log(`Getting customer statistics`);
        return this.customersQueryService.getStatistics();
    }
    async getCustomers(filter, pagination) {
        this.logger.log(`Getting customers`);
        const orderBy = pagination.sortBy ? {
            [pagination.sortBy]: (pagination.sortOrder || 'desc')
        } : { createdAt: client_1.Prisma.SortOrder.desc };
        const [customers, total] = await Promise.all([
            this.customersQueryService.findMany(filter, {
                skip: pagination.skip,
                take: pagination.take,
                orderBy,
            }),
            this.customersQueryService.count(filter),
        ]);
        return {
            data: this.mapToResponseDtoArray(customers),
            total,
            skip: pagination.skip || 0,
            take: pagination.take || 20,
        };
    }
    async getCustomer(id) {
        this.logger.log(`Getting customer ${id}`);
        const customer = await this.customersQueryService.findById(id);
        if (!customer) {
            throw new Error('Customer not found');
        }
        return this.mapToResponseDto(customer);
    }
    async getCustomerByEmail(email) {
        this.logger.log(`Getting customer by email ${email}`);
        const customer = await this.customersQueryService.findByEmail(email);
        return customer ? this.mapToResponseDto(customer) : null;
    }
    async searchCustomers(searchRequest) {
        this.logger.log(`Advanced search`);
        const { filter, pagination } = searchRequest;
        const orderBy = pagination?.sortBy ? {
            [pagination.sortBy]: (pagination.sortOrder || 'desc')
        } : { createdAt: client_1.Prisma.SortOrder.desc };
        const [customers, total] = await Promise.all([
            this.customersQueryService.findMany(filter, {
                skip: pagination?.skip,
                take: pagination?.take,
                orderBy,
            }),
            this.customersQueryService.count(filter),
        ]);
        return {
            data: this.mapToResponseDtoArray(customers),
            total,
            skip: pagination?.skip || 0,
            take: pagination?.take || 20,
        };
    }
};
exports.CustomersQueryController = CustomersQueryController;
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get customer statistics and analytics',
        description: 'Retrieve comprehensive customer statistics including counts by status, type, verification status, and recent activity metrics'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer statistics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number', description: 'Total number of customers' },
                active: { type: 'number', description: 'Number of active customers' },
                inactive: { type: 'number', description: 'Number of inactive customers' },
                suspended: { type: 'number', description: 'Number of suspended customers' },
                pendingVerification: { type: 'number', description: 'Number of customers pending verification' },
                blocked: { type: 'number', description: 'Number of blocked customers' },
                emailVerified: { type: 'number', description: 'Number of customers with verified email' },
                phoneVerified: { type: 'number', description: 'Number of customers with verified phone' },
                kycVerified: { type: 'number', description: 'Number of KYC verified customers' },
                individual: { type: 'number', description: 'Number of individual customers' },
                business: { type: 'number', description: 'Number of business customers' },
                enterprise: { type: 'number', description: 'Number of enterprise customers' },
                recentlyCreated: { type: 'number', description: 'Number of recently created customers' },
                recentlyActive: { type: 'number', description: 'Number of recently active customers' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomersQueryController.prototype, "getCustomerStatistics", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all customers with filtering and pagination',
        description: 'Retrieve a paginated list of customers with optional filtering by status, type, verification status, and search terms'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'List of customers retrieved successfully',
        type: customer_shared_dto_1.CustomerListResponseDto,
    }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term for customer name, email, or phone' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION', 'BLOCKED'], description: 'Filter by customer status' }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, enum: ['INDIVIDUAL', 'BUSINESS', 'ENTERPRISE'], description: 'Filter by customer type' }),
    (0, swagger_1.ApiQuery)({ name: 'isEmailVerified', required: false, type: Boolean, description: 'Filter by email verification status' }),
    (0, swagger_1.ApiQuery)({ name: 'isKycVerified', required: false, type: Boolean, description: 'Filter by KYC verification status' }),
    (0, swagger_1.ApiQuery)({ name: 'tags', required: false, type: [String], description: 'Filter by customer tags' }),
    (0, swagger_1.ApiQuery)({ name: 'skip', required: false, type: Number, description: 'Number of records to skip for pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'take', required: false, type: Number, description: 'Number of records to take (max 100)' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, description: 'Field to sort by (e.g., createdAt, email, firstName)' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Sort order' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [customer_shared_dto_1.CustomerFilterDto,
        customer_shared_dto_1.PaginationDto]),
    __metadata("design:returntype", Promise)
], CustomersQueryController.prototype, "getCustomers", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get customer by ID',
        description: 'Retrieve detailed information for a specific customer by their unique identifier'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer retrieved successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersQueryController.prototype, "getCustomer", null);
__decorate([
    (0, common_1.Get)('email/:email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get customer by email address',
        description: 'Retrieve customer information using their email address as the lookup key'
    }),
    (0, swagger_1.ApiParam)({ name: 'email', description: 'Customer email address' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer retrieved successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersQueryController.prototype, "getCustomerByEmail", null);
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({
        summary: 'Advanced customer search with complex filters',
        description: 'Perform advanced search operations with complex filtering criteria and pagination options'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Search results retrieved successfully',
        type: customer_shared_dto_1.CustomerListResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomersQueryController.prototype, "searchCustomers", null);
exports.CustomersQueryController = CustomersQueryController = CustomersQueryController_1 = __decorate([
    (0, swagger_1.ApiTags)('customers-query'),
    (0, common_1.Controller)('customers'),
    __metadata("design:paramtypes", [customers_query_service_1.CustomersQueryService])
], CustomersQueryController);
//# sourceMappingURL=customers-query.controller.js.map