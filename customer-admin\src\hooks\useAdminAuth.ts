import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { CustomerGraphQLService, User } from '@/services/customer-graphql.service';

interface UseAdminAuthReturn {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  logout: () => void;
}

export function useAdminAuth(): UseAdminAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      if (!CustomerGraphQLService.isAuthenticated()) {
        setIsLoading(false);
        return;
      }

      const currentUser = await CustomerGraphQLService.getCurrentUser();
      
      // Check if user has admin role
      if (currentUser.role !== 'global_admin' && currentUser.role !== 'admin') {
        CustomerGraphQLService.clearAdminToken();
        setIsLoading(false);
        return;
      }

      setUser(currentUser);
    } catch (error) {
      console.error('Authentication check failed:', error);
      CustomerGraphQLService.clearAdminToken();
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    CustomerGraphQLService.clearAdminToken();
    setUser(null);
    router.push('/login');
  };

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    logout,
  };
}
