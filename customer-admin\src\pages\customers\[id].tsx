import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import { withAdminAuth } from '@/components/admin/withAdminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CustomerGraphQLService, Customer } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { 
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Activity,
  CreditCard,
  Building,
  Globe
} from 'lucide-react';
import { format } from 'date-fns';

// Extended customer interface with additional details
interface CustomerDetails extends Customer {
  addresses?: Array<{
    id: string;
    type: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    isDefault: boolean;
  }>;
  contacts?: Array<{
    id: string;
    type: string;
    value: string;
    isPrimary: boolean;
    verified: boolean;
  }>;
  preferences?: {
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
  businessInfo?: {
    companyName?: string;
    taxId?: string;
    industry?: string;
    website?: string;
  };
}

function CustomerDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const [customer, setCustomer] = useState<CustomerDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (id) {
      loadCustomerDetails();
    }
  }, [id]);

  const loadCustomerDetails = async () => {
    try {
      setIsLoading(true);
      const customerData = await CustomerGraphQLService.getCustomerById(id as string);
      
      // Mock additional data since the basic API might not have all details
      const extendedCustomer: CustomerDetails = {
        ...customerData,
        addresses: [
          {
            id: '1',
            type: 'home',
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'USA',
            isDefault: true,
          },
          {
            id: '2',
            type: 'billing',
            street: '456 Business Ave',
            city: 'New York',
            state: 'NY',
            zipCode: '10002',
            country: 'USA',
            isDefault: false,
          },
        ],
        contacts: [
          {
            id: '1',
            type: 'email',
            value: customerData.email,
            isPrimary: true,
            verified: customerData.emailVerified,
          },
          {
            id: '2',
            type: 'phone',
            value: customerData.phone || '******-0123',
            isPrimary: true,
            verified: customerData.phoneVerified,
          },
        ],
        preferences: {
          language: 'en',
          timezone: 'America/New_York',
          notifications: {
            email: true,
            sms: false,
            push: true,
          },
        },
        businessInfo: {
          companyName: 'Example Corp',
          taxId: '12-3456789',
          industry: 'Technology',
          website: 'https://example.com',
        },
      };
      
      setCustomer(extendedCustomer);
    } catch (error) {
      console.error('Failed to load customer details:', error);
      toast.error('Failed to load customer details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyEmail = async () => {
    if (!customer) return;
    
    setIsUpdating(true);
    try {
      await CustomerGraphQLService.verifyCustomerEmail(customer.id);
      toast.success('Email verified successfully');
      loadCustomerDetails();
    } catch (error) {
      console.error('Failed to verify email:', error);
      toast.error('Failed to verify email');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleVerifyPhone = async () => {
    if (!customer) return;
    
    setIsUpdating(true);
    try {
      await CustomerGraphQLService.verifyCustomerPhone(customer.id);
      toast.success('Phone verified successfully');
      loadCustomerDetails();
    } catch (error) {
      console.error('Failed to verify phone:', error);
      toast.error('Failed to verify phone');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleUpdateStatus = async (status: string) => {
    if (!customer) return;
    
    setIsUpdating(true);
    try {
      await CustomerGraphQLService.updateCustomerStatus(customer.id, status);
      toast.success(`Customer status updated to ${status}`);
      loadCustomerDetails();
    } catch (error) {
      console.error('Failed to update status:', error);
      toast.error('Failed to update customer status');
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Loading...">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <User className="h-8 w-8 animate-pulse text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading customer details...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!customer) {
    return (
      <AdminLayout title="Customer Not Found">
        <div className="text-center py-8">
          <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Customer Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The customer you're looking for doesn't exist or has been deleted.
          </p>
          <Button onClick={() => router.push('/customers')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Customers
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`${customer.firstName} ${customer.lastName}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.push('/customers')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h2 className="text-2xl font-bold">
                {customer.firstName} {customer.lastName}
              </h2>
              <p className="text-muted-foreground">Customer ID: {customer.id}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="destructive" size="sm">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Status and Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Status & Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  customer.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {customer.status}
                </span>
                <div className="flex items-center space-x-2">
                  {customer.emailVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <span className="text-sm">Email</span>
                </div>
                <div className="flex items-center space-x-2">
                  {customer.phoneVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <span className="text-sm">Phone</span>
                </div>
                <div className="flex items-center space-x-2">
                  {customer.kycVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  )}
                  <span className="text-sm">KYC</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {!customer.emailVerified && (
                  <Button size="sm" onClick={handleVerifyEmail} disabled={isUpdating}>
                    <Mail className="h-4 w-4 mr-1" />
                    Verify Email
                  </Button>
                )}
                {!customer.phoneVerified && (
                  <Button size="sm" variant="outline" onClick={handleVerifyPhone} disabled={isUpdating}>
                    <Phone className="h-4 w-4 mr-1" />
                    Verify Phone
                  </Button>
                )}
                {customer.status !== 'active' && (
                  <Button size="sm" onClick={() => handleUpdateStatus('active')} disabled={isUpdating}>
                    <Shield className="h-4 w-4 mr-1" />
                    Activate
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Full Name</label>
                <p className="text-sm">{customer.firstName} {customer.lastName}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Email</label>
                <div className="flex items-center space-x-2">
                  <p className="text-sm">{customer.email}</p>
                  {customer.emailVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Phone</label>
                <div className="flex items-center space-x-2">
                  <p className="text-sm">{customer.phone || 'Not provided'}</p>
                  {customer.phone && (customer.phoneVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  ))}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Created</label>
                <p className="text-sm">{format(new Date(customer.createdAt), 'MMM dd, yyyy HH:mm')}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                <p className="text-sm">{format(new Date(customer.updatedAt), 'MMM dd, yyyy HH:mm')}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default withAdminAuth(CustomerDetailPage);
