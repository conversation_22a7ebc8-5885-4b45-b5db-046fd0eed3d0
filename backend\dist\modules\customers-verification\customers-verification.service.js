"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CustomersVerificationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersVerificationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let CustomersVerificationService = CustomersVerificationService_1 = class CustomersVerificationService {
    prisma;
    logger = new common_1.Logger(CustomersVerificationService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async verifyEmail(customerId) {
        this.logger.log(`Verifying email for customer ${customerId}`);
        const customer = await this.prisma.customer.update({
            where: { id: customerId },
            data: {
                isEmailVerified: true,
                updatedAt: new Date(),
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        await this.prisma.auditLog.create({
            data: {
                customerId,
                action: client_1.AuditAction.VERIFICATION,
                entity: 'customer',
                entityId: customerId,
                description: `Email verified`,
                metadata: { verifiedAt: new Date() },
            },
        });
        return customer;
    }
    async verifyPhone(customerId) {
        this.logger.log(`Verifying phone for customer ${customerId}`);
        const customer = await this.prisma.customer.update({
            where: { id: customerId },
            data: {
                isPhoneVerified: true,
                updatedAt: new Date(),
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        await this.prisma.auditLog.create({
            data: {
                customerId,
                action: client_1.AuditAction.VERIFICATION,
                entity: 'customer',
                entityId: customerId,
                description: `Phone verified`,
                metadata: { verifiedAt: new Date() },
            },
        });
        return customer;
    }
    async verifyKyc(customerId) {
        this.logger.log(`Verifying KYC for customer ${customerId}`);
        const customer = await this.prisma.customer.update({
            where: { id: customerId },
            data: {
                isKycVerified: true,
                updatedAt: new Date(),
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        await this.prisma.auditLog.create({
            data: {
                customerId,
                action: client_1.AuditAction.VERIFICATION,
                entity: 'customer',
                entityId: customerId,
                description: `KYC verified`,
                metadata: { verifiedAt: new Date() },
            },
        });
        return customer;
    }
};
exports.CustomersVerificationService = CustomersVerificationService;
exports.CustomersVerificationService = CustomersVerificationService = CustomersVerificationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CustomersVerificationService);
//# sourceMappingURL=customers-verification.service.js.map