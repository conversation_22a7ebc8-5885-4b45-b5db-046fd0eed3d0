import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { withAdminAuth } from '@/components/admin/withAdminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CustomerGraphQLService } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { 
  Settings as SettingsIcon, 
  Save, 
  RefreshCw,
  Database,
  Shield,
  Mail,
  Phone,
  Globe,
  Key,
  Users,
  Bell,
  Palette,
  Monitor,
  Server,
  AlertTriangle
} from 'lucide-react';

interface SystemSettings {
  general: {
    siteName: string;
    siteUrl: string;
    adminEmail: string;
    timezone: string;
  };
  authentication: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireTwoFactor: boolean;
  };
  verification: {
    emailVerificationRequired: boolean;
    phoneVerificationRequired: boolean;
    kycVerificationRequired: boolean;
    autoVerifyEmail: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    webhookUrl: string;
    slackWebhook: string;
  };
  database: {
    connectionStatus: 'connected' | 'disconnected' | 'error';
    lastBackup: string;
    recordCount: number;
  };
}

function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>({
    general: {
      siteName: process.env.NEXT_PUBLIC_APP_NAME || 'Customer Management System',
      siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://admin.example.com',
      adminEmail: process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>',
      timezone: 'UTC',
    },
    authentication: {
      sessionTimeout: 3600,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      requireTwoFactor: false,
    },
    verification: {
      emailVerificationRequired: true,
      phoneVerificationRequired: true,
      kycVerificationRequired: false,
      autoVerifyEmail: false,
    },
    notifications: {
      emailNotifications: true,
      smsNotifications: false,
      webhookUrl: '',
      slackWebhook: '',
    },
    database: {
      connectionStatus: 'connected',
      lastBackup: new Date().toISOString(),
      recordCount: 0,
    },
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      
      // Try to get some real data for database status
      try {
        const customersData = await CustomerGraphQLService.getCustomers(1, 1);
        setSettings(prev => ({
          ...prev,
          database: {
            ...prev.database,
            connectionStatus: 'connected',
            recordCount: customersData.total,
          },
        }));
      } catch (error) {
        setSettings(prev => ({
          ...prev,
          database: {
            ...prev.database,
            connectionStatus: 'error',
          },
        }));
      }
      
    } catch (error) {
      console.error('Failed to load settings:', error);
      toast.error('Failed to load system settings');
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setIsSaving(true);
      
      // In a real implementation, this would save to the backend
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (section: keyof SystemSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
  };

  const testConnection = async () => {
    try {
      await CustomerGraphQLService.getCurrentUser();
      setSettings(prev => ({
        ...prev,
        database: {
          ...prev.database,
          connectionStatus: 'connected',
        },
      }));
      toast.success('Database connection successful');
    } catch (error) {
      setSettings(prev => ({
        ...prev,
        database: {
          ...prev.database,
          connectionStatus: 'error',
        },
      }));
      toast.error('Database connection failed');
    }
  };

  return (
    <AdminLayout title="Settings">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">System Settings</h2>
            <p className="text-muted-foreground">
              Configure system-wide settings and preferences
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={loadSettings} disabled={isLoading}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={saveSettings} disabled={isSaving}>
              {isSaving ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        </div>

        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>General Settings</span>
            </CardTitle>
            <CardDescription>
              Basic system configuration and site information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="siteName">Site Name</Label>
                <Input
                  id="siteName"
                  value={settings.general.siteName}
                  onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="siteUrl">Site URL</Label>
                <Input
                  id="siteUrl"
                  value={settings.general.siteUrl}
                  onChange={(e) => updateSetting('general', 'siteUrl', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="adminEmail">Admin Email</Label>
                <Input
                  id="adminEmail"
                  type="email"
                  value={settings.general.adminEmail}
                  onChange={(e) => updateSetting('general', 'adminEmail', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">Timezone</Label>
                <select
                  id="timezone"
                  value={settings.general.timezone}
                  onChange={(e) => updateSetting('general', 'timezone', e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                >
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Authentication Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Authentication & Security</span>
            </CardTitle>
            <CardDescription>
              Configure authentication and security policies
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="sessionTimeout">Session Timeout (seconds)</Label>
                <Input
                  id="sessionTimeout"
                  type="number"
                  value={settings.authentication.sessionTimeout}
                  onChange={(e) => updateSetting('authentication', 'sessionTimeout', parseInt(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                <Input
                  id="maxLoginAttempts"
                  type="number"
                  value={settings.authentication.maxLoginAttempts}
                  onChange={(e) => updateSetting('authentication', 'maxLoginAttempts', parseInt(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
                <Input
                  id="passwordMinLength"
                  type="number"
                  value={settings.authentication.passwordMinLength}
                  onChange={(e) => updateSetting('authentication', 'passwordMinLength', parseInt(e.target.value))}
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="requireTwoFactor"
                  checked={settings.authentication.requireTwoFactor}
                  onChange={(e) => updateSetting('authentication', 'requireTwoFactor', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="requireTwoFactor">Require Two-Factor Authentication</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Verification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Customer Verification</span>
            </CardTitle>
            <CardDescription>
              Configure customer verification requirements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="emailVerificationRequired"
                  checked={settings.verification.emailVerificationRequired}
                  onChange={(e) => updateSetting('verification', 'emailVerificationRequired', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="emailVerificationRequired">Email Verification Required</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="phoneVerificationRequired"
                  checked={settings.verification.phoneVerificationRequired}
                  onChange={(e) => updateSetting('verification', 'phoneVerificationRequired', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="phoneVerificationRequired">Phone Verification Required</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="kycVerificationRequired"
                  checked={settings.verification.kycVerificationRequired}
                  onChange={(e) => updateSetting('verification', 'kycVerificationRequired', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="kycVerificationRequired">KYC Verification Required</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="autoVerifyEmail"
                  checked={settings.verification.autoVerifyEmail}
                  onChange={(e) => updateSetting('verification', 'autoVerifyEmail', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="autoVerifyEmail">Auto-verify Email (Development)</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Notifications</span>
            </CardTitle>
            <CardDescription>
              Configure notification settings and webhooks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="emailNotifications"
                  checked={settings.notifications.emailNotifications}
                  onChange={(e) => updateSetting('notifications', 'emailNotifications', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="emailNotifications">Email Notifications</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="smsNotifications"
                  checked={settings.notifications.smsNotifications}
                  onChange={(e) => updateSetting('notifications', 'smsNotifications', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="smsNotifications">SMS Notifications</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="webhookUrl">Webhook URL</Label>
                <Input
                  id="webhookUrl"
                  placeholder="https://your-webhook-url.com"
                  value={settings.notifications.webhookUrl}
                  onChange={(e) => updateSetting('notifications', 'webhookUrl', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slackWebhook">Slack Webhook</Label>
                <Input
                  id="slackWebhook"
                  placeholder="https://hooks.slack.com/..."
                  value={settings.notifications.slackWebhook}
                  onChange={(e) => updateSetting('notifications', 'slackWebhook', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Database Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Database Status</span>
            </CardTitle>
            <CardDescription>
              Monitor database connection and statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label>Connection Status</Label>
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    settings.database.connectionStatus === 'connected' ? 'bg-green-500' :
                    settings.database.connectionStatus === 'error' ? 'bg-red-500' :
                    'bg-yellow-500'
                  }`} />
                  <span className="capitalize">{settings.database.connectionStatus}</span>
                  <Button variant="outline" size="sm" onClick={testConnection}>
                    Test Connection
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Total Records</Label>
                <p className="text-2xl font-bold">{settings.database.recordCount}</p>
              </div>
              <div className="space-y-2">
                <Label>Last Backup</Label>
                <p className="text-sm text-muted-foreground">
                  {new Date(settings.database.lastBackup).toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default withAdminAuth(SettingsPage);
