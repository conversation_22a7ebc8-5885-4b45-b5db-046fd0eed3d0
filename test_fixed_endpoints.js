const http = require('http');

// Your access token cookie value
const accessToken = 'SUM87jE0gKfD78z8KPera8uWTUFqppyWXLAwTbE14GpFkC6lEsjqSUlcVqao%2FEUE6ogMFdQuRqHNU1fbBSwqK8rClMOg5nLz5Wa7bnkvx%2B1HbXrhgQViHjxwdrrx4dJ8Jr1N8PrdbLkZeYd4RfGlCWlHJMn7Uf%2BmdZmZ%2BvgXIAjWvBVTXybhr%2Bgfq4HZ2%2BzSc%2BwLNQ%2ByfU2Okfjye2Or7xH4JJQqUAvemCS5V8gCRyNI7Eye5Mp3GjGdYAg6ast90RwobzNbOOvIbSNaPxACgHV9ipBVUN%2F%2FhLQCw788W%2BYgtziS%2FV4c7e6256nssy6aasCX9Qf8adC4q56W50Si6f23C0InPzYdoStTP7KQwxOL8TSEIlSLPgw0of3GhBemFkOyUyYXR94vcx52D7kgAp6f6HirP9tJqJPX3q7iit%2BwQ7Iy5sTiBuiS1cAvy%2FhppgK%2BEs1kCn3jV6PGKsp%2B6%2Bvz3I6r6l7buCeA%2BoKB%2BadsT1KZgexEzj%2FV14KC8vWC1%2F7gBxmEvspYS6yw4HAObwlQjITkm%2F0GZbfsogB6LNoCY9oC2JDrpO%2Fi7TXtLMfCN7HoIv9s%2BgviyjrteBuNyngzkrVYo%2F%2BS1d8O4okjnYnLFthds7%2Bjy7RWcI3dWvVpbFnH2py1yl%2Bsce1BReqKr%2FUAs40%2Bn3%2FCMOYD3R2V1GjeU5FPr1hsYrD0NhT9rGDm%2Fc0RTZnHJ8RUDBsAW07ngQ66koiF50WLtdUdNA9Kmk%2F6iZcsYIfDT5vTR0M0DNUU8DAgjA448EfD39NC5ZnzAIkdAMuAtfNDp4PBXZepDtWufrugeAvUBB2gN9Odg%2BvqSH0U8TGMGjYlZTHYaXNqqUHgVarJAskdI8LefAD5LHQv%2Bbjxn5tVFUqTtnEhfqNBfhvGp%2Brz4ePCSwPXLdPuhL8K2SyVlrzvaOpeqQHqBcUbB7XU4UNzAeqUlbmbWauDylCKgbfd9VNVNfRm--sKsro5KGD%2B%2Fx6No8--1ib9AOCPWxCSqtjos%2BM2LA%3D%3D';

function testEndpoint(method, path, description) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3060,
      path: path,
      method: method,
      headers: {
        'Cookie': `access_token=${accessToken}`,
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`\n=== ${description} ===`);
        console.log(`${method} ${path}`);
        console.log(`Status: ${res.statusCode}`);
        
        try {
          const jsonData = JSON.parse(data);
          console.log(`Response: ${JSON.stringify(jsonData, null, 2)}`);
        } catch (e) {
          console.log(`Response: ${data}`);
        }
        
        resolve({ status: res.statusCode, data: data });
      });
    });

    req.on('error', (error) => {
      console.error(`Error testing ${path}:`, error);
      reject(error);
    });

    req.end();
  });
}

async function runFixedEndpointTests() {
  console.log('🔧 TESTING FIXED AUTHENTICATION ENDPOINTS');
  console.log('=========================================\n');
  console.log('This test verifies that the /auth/me/cookies endpoint has been restored');
  console.log('to use local JWT verification instead of external service calls.\n');
  
  try {
    // Test the fixed /auth/me/cookies endpoint
    await testEndpoint('GET', '/auth/me/cookies', 'Fixed /auth/me/cookies endpoint (should use local JWT verification)');
    
    // Test other endpoints for comparison
    await testEndpoint('GET', '/auth/me', 'Legacy /auth/me endpoint');
    await testEndpoint('GET', '/auth/status', 'Auth status endpoint');
    await testEndpoint('GET', '/auth/users', 'Users endpoint (should return current user only)');
    
    console.log('\n✅ Fixed endpoint testing completed!');
    console.log('\n📊 EXPECTED RESULTS:');
    console.log('- /auth/me/cookies should return 200 with user data (local JWT verification)');
    console.log('- /auth/me should return 200 with user data (legacy endpoint)');
    console.log('- /auth/status should return 200 with service status');
    console.log('- /auth/users should return 200 with current user only');
    console.log('\n📋 REGRESSION FIXED:');
    console.log('- /auth/me/cookies now uses local JWT verification again');
    console.log('- External service integration preserved for debugging');
    console.log('- All endpoints should work correctly');
    
  } catch (error) {
    console.error('❌ Testing failed:', error);
  }
}

runFixedEndpointTests();
