import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { withAdminAuth } from '@/components/admin/withAdminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CustomerGraphQLService, AuditLog } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { 
  Activity, 
  Search, 
  Filter, 
  Download,
  RefreshCw,
  User,
  Mail,
  Phone,
  Shield,
  Edit,
  Trash2,
  Plus,
  Eye,
  Clock,
  MapPin,
  Monitor
} from 'lucide-react';
import { format } from 'date-fns';

function ActivityLogPage() {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalLogs, setTotalLogs] = useState(0);
  const limit = 20;

  // Mock data since we might not have real audit logs yet
  const mockAuditLogs: AuditLog[] = [
    {
      id: '1',
      action: 'CREATE',
      entityType: 'Customer',
      entityId: 'cust_001',
      userId: 'admin_001',
      userEmail: '<EMAIL>',
      oldValues: null,
      newValues: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      action: 'VERIFY_EMAIL',
      entityType: 'Customer',
      entityId: 'cust_001',
      userId: 'admin_001',
      userEmail: '<EMAIL>',
      oldValues: { emailVerified: false },
      newValues: { emailVerified: true },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date(Date.now() - 3600000).toISOString(),
    },
    {
      id: '3',
      action: 'UPDATE',
      entityType: 'Customer',
      entityId: 'cust_002',
      userId: 'admin_001',
      userEmail: '<EMAIL>',
      oldValues: { status: 'pending' },
      newValues: { status: 'active' },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date(Date.now() - 7200000).toISOString(),
    },
    {
      id: '4',
      action: 'VERIFY_PHONE',
      entityType: 'Customer',
      entityId: 'cust_003',
      userId: 'admin_002',
      userEmail: '<EMAIL>',
      oldValues: { phoneVerified: false },
      newValues: { phoneVerified: true },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date(Date.now() - 10800000).toISOString(),
    },
    {
      id: '5',
      action: 'LOGIN',
      entityType: 'User',
      entityId: 'admin_001',
      userId: 'admin_001',
      userEmail: '<EMAIL>',
      oldValues: null,
      newValues: null,
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date(Date.now() - 14400000).toISOString(),
    },
  ];

  useEffect(() => {
    loadAuditLogs();
  }, [currentPage, searchTerm, filterAction]);

  const loadAuditLogs = async () => {
    try {
      setIsLoading(true);
      
      // Try to load real audit logs, fall back to mock data
      try {
        const data = await CustomerGraphQLService.getAuditLogs(
          currentPage,
          limit,
          searchTerm || undefined
        );
        setAuditLogs(data.data);
        setTotalLogs(data.total);
      } catch (error) {
        // Use mock data if real API is not available
        console.log('Using mock audit log data');
        let filtered = mockAuditLogs;
        
        if (searchTerm) {
          filtered = filtered.filter(log =>
            log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.entityType.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.userEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.entityId.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (filterAction !== 'all') {
          filtered = filtered.filter(log => log.action === filterAction);
        }
        
        setAuditLogs(filtered);
        setTotalLogs(filtered.length);
      }
      
    } catch (error) {
      console.error('Failed to load audit logs:', error);
      toast.error('Failed to load activity logs');
    } finally {
      setIsLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'CREATE':
        return <Plus className="h-4 w-4 text-green-600" />;
      case 'UPDATE':
        return <Edit className="h-4 w-4 text-blue-600" />;
      case 'DELETE':
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case 'VERIFY_EMAIL':
        return <Mail className="h-4 w-4 text-purple-600" />;
      case 'VERIFY_PHONE':
        return <Phone className="h-4 w-4 text-purple-600" />;
      case 'VERIFY_KYC':
        return <Shield className="h-4 w-4 text-purple-600" />;
      case 'LOGIN':
        return <User className="h-4 w-4 text-gray-600" />;
      case 'VIEW':
        return <Eye className="h-4 w-4 text-gray-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'CREATE':
        return 'bg-green-100 text-green-800';
      case 'UPDATE':
        return 'bg-blue-100 text-blue-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      case 'VERIFY_EMAIL':
      case 'VERIFY_PHONE':
      case 'VERIFY_KYC':
        return 'bg-purple-100 text-purple-800';
      case 'LOGIN':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatChanges = (oldValues: any, newValues: any) => {
    if (!oldValues && !newValues) return null;
    
    if (!oldValues) {
      return (
        <div className="text-sm text-green-600">
          Created: {JSON.stringify(newValues, null, 2)}
        </div>
      );
    }
    
    if (!newValues) {
      return (
        <div className="text-sm text-red-600">
          Deleted: {JSON.stringify(oldValues, null, 2)}
        </div>
      );
    }
    
    return (
      <div className="text-sm space-y-1">
        {Object.keys(newValues).map(key => (
          <div key={key} className="flex items-center space-x-2">
            <span className="font-medium">{key}:</span>
            <span className="text-red-600 line-through">{oldValues[key]}</span>
            <span>→</span>
            <span className="text-green-600">{newValues[key]}</span>
          </div>
        ))}
      </div>
    );
  };

  const totalPages = Math.ceil(totalLogs / limit);

  return (
    <AdminLayout title="Activity Log">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Activity Log</h2>
            <p className="text-muted-foreground">
              Monitor all system activities and audit trails
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={loadAuditLogs}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
            <CardDescription>
              Find specific activities and audit entries
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by action, entity, user, or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">All Actions</option>
                <option value="CREATE">Create</option>
                <option value="UPDATE">Update</option>
                <option value="DELETE">Delete</option>
                <option value="VERIFY_EMAIL">Email Verification</option>
                <option value="VERIFY_PHONE">Phone Verification</option>
                <option value="VERIFY_KYC">KYC Verification</option>
                <option value="LOGIN">Login</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Activity Log */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Entries ({totalLogs})</CardTitle>
            <CardDescription>
              Chronological list of all system activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Activity className="h-6 w-6 animate-pulse mr-2" />
                <span>Loading activity log...</span>
              </div>
            ) : auditLogs.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-lg font-medium">No activities found</p>
                <p className="text-muted-foreground">Try adjusting your search criteria</p>
              </div>
            ) : (
              <div className="space-y-4">
                {auditLogs.map((log) => (
                  <div key={log.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        {getActionIcon(log.action)}
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>
                              {log.action}
                            </span>
                            <span className="text-sm font-medium">{log.entityType}</span>
                            <span className="text-sm text-muted-foreground">#{log.entityId}</span>
                          </div>
                          
                          <div className="text-sm text-muted-foreground">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{log.userEmail || 'System'}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>{format(new Date(log.createdAt), 'MMM dd, yyyy HH:mm:ss')}</span>
                              </div>
                              {log.ipAddress && (
                                <div className="flex items-center space-x-1">
                                  <MapPin className="h-3 w-3" />
                                  <span>{log.ipAddress}</span>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          {(log.oldValues || log.newValues) && (
                            <div className="mt-2 p-2 bg-muted rounded">
                              {formatChanges(log.oldValues, log.newValues)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages} ({totalLogs} total entries)
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default withAdminAuth(ActivityLogPage);
