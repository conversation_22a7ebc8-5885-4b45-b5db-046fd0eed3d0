"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthSharedController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthSharedController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const auth_guard_1 = require("./auth.guard");
const decrypt_dto_1 = require("./dto/decrypt.dto");
const jwt = require("jsonwebtoken");
let AuthSharedController = AuthSharedController_1 = class AuthSharedController {
    authSharedService;
    logger = new common_1.Logger(AuthSharedController_1.name);
    constructor(authSharedService) {
        this.authSharedService = authSharedService;
    }
    async getCurrentUserFromCookies(request, reply) {
        try {
            const cookies = request.cookies || {};
            this.logger.log(`Received cookies: ${JSON.stringify(Object.keys(cookies))}`);
            this.logger.log(`Access token present: ${!!cookies.access_token}`);
            this.logger.log(`Refresh token present: ${!!cookies.refresh_token}`);
            const user = await this.authSharedService.authenticateFromCookies(cookies);
            this.logger.log(`User authenticated via cookies: ${user.email}`);
            return reply.status(common_1.HttpStatus.OK).send(user);
        }
        catch (error) {
            this.logger.error('Cookie authentication failed:', error);
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
    async logout(request, reply) {
        try {
            this.logger.log('🔐 [AUTH CONTROLLER] Logout endpoint called');
            reply.cookie('access_token', '', {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                domain: process.env.COOKIE_DOMAIN || undefined,
                path: '/',
                expires: new Date(0)
            });
            reply.cookie('refresh_token', '', {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                domain: process.env.COOKIE_DOMAIN || undefined,
                path: '/',
                expires: new Date(0)
            });
            this.logger.log('✅ [AUTH CONTROLLER] Authentication cookies cleared successfully');
            return reply.status(common_1.HttpStatus.OK).send({
                success: true,
                message: 'Logout successful'
            });
        }
        catch (error) {
            this.logger.error(`❌ [AUTH CONTROLLER] Logout error: ${error.message}`);
            return reply.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).send({
                success: false,
                message: 'Logout failed'
            });
        }
    }
    async refreshAuthentication(request, reply) {
        try {
            const cookies = request.cookies || {};
            const success = await this.authSharedService.refreshAuthentication(cookies);
            if (success) {
                this.logger.log('Authentication refreshed successfully');
                return reply.status(common_1.HttpStatus.OK).send({
                    success: true,
                    message: 'Authentication refreshed successfully'
                });
            }
            else {
                this.logger.warn('Authentication refresh failed');
                throw new common_1.UnauthorizedException('Token refresh failed');
            }
        }
        catch (error) {
            this.logger.error('Token refresh error:', error);
            throw new common_1.UnauthorizedException('Token refresh failed');
        }
    }
    async getCurrentUser(request, reply) {
        return this.getCurrentUserFromCookies(request, reply);
    }
    getAuthStatus() {
        return {
            status: 'ok',
            service: 'auth-shared',
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };
    }
    debugCookies(request) {
        return {
            cookies: request.cookies || {},
            headers: {
                cookie: request.headers.cookie,
                origin: request.headers.origin,
                referer: request.headers.referer,
                host: request.headers.host,
                'user-agent': request.headers['user-agent'],
            },
            url: request.url,
            method: request.method,
            timestamp: new Date().toISOString(),
        };
    }
    async decryptToken(body) {
        try {
            const decrypted = await this.authSharedService.decryptTokenForTesting(body.token);
            let type = 'unknown';
            if (decrypted.startsWith('eyJ')) {
                type = 'jwt';
            }
            else if (/^[0-9a-fA-F]+$/.test(decrypted)) {
                type = 'hex';
            }
            return {
                decrypted,
                type,
                length: decrypted.length
            };
        }
        catch (error) {
            this.logger.error('Failed to decrypt token:', error);
            throw new common_1.UnauthorizedException('Failed to decrypt token');
        }
    }
    decodeJwt(body) {
        try {
            const decoded = jwt.decode(body.jwt, { complete: true });
            if (!decoded) {
                throw new Error('Invalid JWT format');
            }
            return {
                header: decoded.header,
                payload: decoded.payload,
                signature: decoded.signature
            };
        }
        catch (error) {
            this.logger.error('Failed to decode JWT:', error);
            throw new common_1.UnauthorizedException('Failed to decode JWT');
        }
    }
    async getUserById(id, request, reply) {
        try {
            const cookies = request.cookies || {};
            this.logger.log(`Getting user by ID: ${id}`);
            const user = await this.authSharedService.getUserById(id, cookies);
            return reply.status(common_1.HttpStatus.OK).send(user);
        }
        catch (error) {
            this.logger.error('Get user by ID failed:', error);
            throw new common_1.UnauthorizedException(error.message || 'Failed to retrieve user');
        }
    }
    async getAllUsers(request, reply) {
        try {
            const cookies = request.cookies || {};
            this.logger.log('Getting all users');
            const users = await this.authSharedService.getAllUsers(cookies);
            return reply.status(common_1.HttpStatus.OK).send(users);
        }
        catch (error) {
            this.logger.error('Get all users failed:', error);
            throw new common_1.UnauthorizedException(error.message || 'Failed to retrieve users');
        }
    }
};
exports.AuthSharedController = AuthSharedController;
__decorate([
    (0, common_1.Get)('me/cookies'),
    (0, swagger_1.ApiCookieAuth)('access_token'),
    (0, swagger_1.ApiCookieAuth)('refresh_token'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get current user from cookies',
        description: 'Authenticates user using encrypted cookies and returns user information'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'User authenticated successfully',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                username: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                role: { type: 'string' },
                permissions: { type: 'array', items: { type: 'string' } },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' }
            }
        }
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Authentication failed - invalid or missing cookies',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 401 },
                message: { type: 'string', example: 'Authentication failed' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthSharedController.prototype, "getCurrentUserFromCookies", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, swagger_1.ApiCookieAuth)('access_token'),
    (0, swagger_1.ApiCookieAuth)('refresh_token'),
    (0, swagger_1.ApiOperation)({
        summary: 'Logout user',
        description: 'Clears authentication cookies and logs out the user'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Logout successful',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Logout successful' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Logout failed',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 500 },
                message: { type: 'string', example: 'Logout failed' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthSharedController.prototype, "logout", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, swagger_1.ApiCookieAuth)('refresh_token'),
    (0, swagger_1.ApiOperation)({
        summary: 'Refresh authentication tokens',
        description: 'Validates refresh token from cookies and confirms authentication status'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Token refresh successful',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Authentication refreshed successfully' }
            }
        }
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Refresh failed - invalid or expired refresh token',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 401 },
                message: { type: 'string', example: 'Token refresh failed' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthSharedController.prototype, "refreshAuthentication", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get current user (legacy endpoint)',
        description: 'Legacy endpoint for getting current user - redirects to cookie-based auth'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'User authenticated successfully',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                username: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                role: { type: 'string' },
                permissions: { type: 'array', items: { type: 'string' } },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' }
            }
        }
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Authentication failed',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 401 },
                message: { type: 'string', example: 'Authentication failed' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthSharedController.prototype, "getCurrentUser", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, auth_guard_1.Public)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get authentication service status',
        description: 'Public endpoint to check if authentication service is running'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Authentication service status',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', example: 'ok' },
                service: { type: 'string', example: 'auth-shared' },
                timestamp: { type: 'string' },
                version: { type: 'string', example: '1.0.0' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AuthSharedController.prototype, "getAuthStatus", null);
__decorate([
    (0, common_1.Get)('debug/cookies'),
    (0, auth_guard_1.Public)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Debug cookie information',
        description: 'Debug endpoint to see all cookies and headers for troubleshooting'
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AuthSharedController.prototype, "debugCookies", null);
__decorate([
    (0, common_1.Post)('decrypt'),
    (0, auth_guard_1.Public)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Decrypt encrypted token',
        description: 'Decrypt an encrypted access token or refresh token for testing purposes'
    }),
    (0, swagger_1.ApiBody)({ type: decrypt_dto_1.DecryptTokenDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Token decrypted successfully',
        schema: {
            type: 'object',
            properties: {
                decrypted: { type: 'string', description: 'The decrypted token' },
                type: { type: 'string', description: 'Type of token (jwt or hex)' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid encrypted token format'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [decrypt_dto_1.DecryptTokenDto]),
    __metadata("design:returntype", Promise)
], AuthSharedController.prototype, "decryptToken", null);
__decorate([
    (0, common_1.Post)('decode-jwt'),
    (0, auth_guard_1.Public)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Decode JWT token',
        description: 'Decode a JWT token to see its payload (without verification)'
    }),
    (0, swagger_1.ApiBody)({ type: decrypt_dto_1.DecodeJwtDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'JWT decoded successfully',
        schema: {
            type: 'object',
            properties: {
                header: { type: 'object', description: 'JWT header' },
                payload: { type: 'object', description: 'JWT payload' },
                signature: { type: 'string', description: 'JWT signature' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid JWT format'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [decrypt_dto_1.DecodeJwtDto]),
    __metadata("design:returntype", void 0)
], AuthSharedController.prototype, "decodeJwt", null);
__decorate([
    (0, common_1.Get)('users/:id'),
    (0, swagger_1.ApiCookieAuth)('access_token'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get user by ID',
        description: 'Retrieve user information by ID from external auth service using encrypted access token'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'User unique identifier (UUID)',
        type: 'string',
        format: 'uuid'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'User retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                first_name: { type: 'string' },
                last_name: { type: 'string' }
            }
        }
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Authentication failed or user not found',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 401 },
                message: { type: 'string', example: 'Invalid or expired access token' }
            }
        }
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AuthSharedController.prototype, "getUserById", null);
__decorate([
    (0, common_1.Get)('users'),
    (0, swagger_1.ApiCookieAuth)('access_token'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all users',
        description: 'Retrieve all users from external auth service using encrypted access token'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Users retrieved successfully',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    first_name: { type: 'string' },
                    last_name: { type: 'string' }
                }
            }
        }
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Authentication failed',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 401 },
                message: { type: 'string', example: 'Invalid or expired access token' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthSharedController.prototype, "getAllUsers", null);
exports.AuthSharedController = AuthSharedController = AuthSharedController_1 = __decorate([
    (0, swagger_1.ApiTags)('Authentication'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthSharedService])
], AuthSharedController);
//# sourceMappingURL=auth.controller.js.map