# API configuration
API_PORT=3001
API_PREFIX=api/v1

# Swagger API documentation
SWAGGER_ENABLE=true

# Database ORM configuration
DATABASE_URL=postgresql://nestjs:password@localhost:5432/nestjs?schema=public

# JWT configuration
JWT_SECRET=your_jwt_secret

# Access to the health route
HEALTH_TOKEN=ThisMustBeChanged

# Logging configuration
LOG=true

# OpenObserve Logging configuration
OTEL_LOGS=false
# OTEL_USER=<EMAIL>
# OTEL_PASSWORD=YourPassword
# OTEL_HOST=https://ngnair-logs.ngcap.ngnair.com
# OTEL_ORG=default
# OTEL_STREAM=quickstart1

# Redis PubSub configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Application Settings
PORT=3001
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3001,https://your-app.com

AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com