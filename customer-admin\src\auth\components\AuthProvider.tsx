import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { AuthState, AuthContextType, User } from '../types/auth.types';
import { authService } from '../services/authService';

export const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      console.log('🔐 AuthProvider - Starting auth initialization');
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if user has auth cookies locally
      const hasCookies = authService.hasAuthCookies();
      console.log('🍪 AuthProvider - Has local cookies:', hasCookies);

      // Always try to get user info from backend, even if local cookies aren't found
      // The backend might be able to access cookies that the frontend can't see due to domain issues
      console.log('🔍 AuthProvider - Attempting to get user info from backend (regardless of local cookie status)');
      // Try to get user info from backend
      const user = await authService.getCurrentUser();
      console.log('👤 AuthProvider - User from backend:', user ? 'User found' : 'No user');

      if (user) {
        console.log('✅ AuthProvider - Setting authenticated state');
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      } else {
        console.log('❌ AuthProvider - No user returned, setting unauthenticated state');
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      }
    } catch (error) {
      console.error('❌ Auth initialization error:', error);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      });
    }
  };

  const login = (redirectUrl?: string) => {
    authService.login(redirectUrl);
  };

  const logout = () => {
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    authService.logout();
  };

  const refreshAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const success = await authService.refreshAuth();
      
      if (success) {
        const user = await authService.getCurrentUser();
        setAuthState({
          user,
          isAuthenticated: !!user,
          isLoading: false,
          error: null,
        });
      } else {
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: 'Failed to refresh authentication',
        });
      }
    } catch (error) {
      console.error('Auth refresh error:', error);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to refresh authentication',
      });
    }
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
