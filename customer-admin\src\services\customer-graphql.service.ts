import { gql } from '@apollo/client';
import { apolloClient } from '@/lib/apollo-client';

// GraphQL Types
export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  permissions?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthMeResponse {
  me: User;
}

export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  status: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  kycVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomersResponse {
  customers: {
    data: Customer[];
    total: number;
    page: number;
    limit: number;
  };
}

// GraphQL Queries
export const AUTH_ME_QUERY = gql`
  query AuthMe {
    me {
      id
      username
      email
      role
      permissions
      createdAt
      updatedAt
    }
  }
`;

export const GET_CUSTOMERS_QUERY = gql`
  query GetCustomers($page: Int, $limit: Int, $filter: String) {
    customers(page: $page, limit: $limit, filter: $filter) {
      data {
        id
        firstName
        lastName
        email
        phone
        status
        emailVerified
        phoneVerified
        kycVerified
        createdAt
        updatedAt
      }
      total
      page
      limit
    }
  }
`;

export const GET_CUSTOMER_BY_ID_QUERY = gql`
  query GetCustomerById($id: ID!) {
    customer(id: $id) {
      id
      firstName
      lastName
      email
      phone
      status
      emailVerified
      phoneVerified
      kycVerified
      createdAt
      updatedAt
    }
  }
`;

// GraphQL Mutations
export const UPDATE_CUSTOMER_STATUS_MUTATION = gql`
  mutation UpdateCustomerStatus($id: ID!, $status: String!) {
    updateCustomerStatus(id: $id, status: $status) {
      id
      status
      updatedAt
    }
  }
`;

export const VERIFY_CUSTOMER_EMAIL_MUTATION = gql`
  mutation VerifyCustomerEmail($id: ID!) {
    verifyCustomerEmail(id: $id) {
      id
      emailVerified
      updatedAt
    }
  }
`;

export const VERIFY_CUSTOMER_PHONE_MUTATION = gql`
  mutation VerifyCustomerPhone($id: ID!) {
    verifyCustomerPhone(id: $id) {
      id
      phoneVerified
      updatedAt
    }
  }
`;

// Audit Log Query
export const GET_AUDIT_LOGS_QUERY = gql`
  query GetAuditLogs($page: Int, $limit: Int, $filter: String) {
    auditLogs(page: $page, limit: $limit, filter: $filter) {
      data {
        id
        action
        entityType
        entityId
        userId
        userEmail
        oldValues
        newValues
        ipAddress
        userAgent
        createdAt
      }
      total
      page
      limit
    }
  }
`;

// Types for Audit Logs
export interface AuditLog {
  id: string;
  action: string;
  entityType: string;
  entityId: string;
  userId?: string;
  userEmail?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

export interface AuditLogsResponse {
  auditLogs: {
    data: AuditLog[];
    total: number;
    page: number;
    limit: number;
  };
}

// Service Class
export class CustomerGraphQLService {
  /**
   * Get current user profile (returns mock user since auth is disabled)
   */
  static async getCurrentUser(): Promise<User> {
    // Return a mock user since authentication is disabled
    const mockUser: User = {
      id: 'mock-admin-001',
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      permissions: ['read', 'write', 'admin'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return mockUser;
  }

  /**
   * Get customers with pagination and filtering
   */
  static async getCustomers(page = 1, limit = 10, filter?: string): Promise<CustomersResponse['customers']> {
    try {
      const { data } = await apolloClient.query<CustomersResponse>({
        query: GET_CUSTOMERS_QUERY,
        variables: { page, limit, filter },
      });
      return data.customers;
    } catch (error) {
      console.error('Failed to get customers:', error);

      // Provide mock data for development
      if (error.networkError && process.env.NODE_ENV === 'development') {
        console.warn('Using mock customers data for development');

        const mockCustomers: Customer[] = [
          {
            id: 'cust-001',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '******-0123',
            status: 'active',
            emailVerified: true,
            phoneVerified: true,
            kycVerified: false,
            createdAt: '2024-01-15T10:30:00Z',
            updatedAt: '2024-01-20T14:45:00Z',
          },
          {
            id: 'cust-002',
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '******-0124',
            status: 'pending',
            emailVerified: false,
            phoneVerified: false,
            kycVerified: false,
            createdAt: '2024-01-16T09:15:00Z',
            updatedAt: '2024-01-16T09:15:00Z',
          },
          {
            id: 'cust-003',
            firstName: 'Bob',
            lastName: 'Johnson',
            email: '<EMAIL>',
            phone: '******-0125',
            status: 'active',
            emailVerified: true,
            phoneVerified: false,
            kycVerified: true,
            createdAt: '2024-01-10T16:20:00Z',
            updatedAt: '2024-01-18T11:30:00Z',
          },
        ];

        // Apply basic filtering if provided
        let filteredCustomers = mockCustomers;
        if (filter) {
          filteredCustomers = mockCustomers.filter(customer =>
            customer.firstName.toLowerCase().includes(filter.toLowerCase()) ||
            customer.lastName.toLowerCase().includes(filter.toLowerCase()) ||
            customer.email.toLowerCase().includes(filter.toLowerCase())
          );
        }

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);

        return {
          data: paginatedCustomers,
          total: filteredCustomers.length,
          page,
          limit,
        };
      }

      throw error;
    }
  }

  /**
   * Get customer by ID
   */
  static async getCustomerById(id: string): Promise<Customer> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CUSTOMER_BY_ID_QUERY,
        variables: { id },
      });
      return data.customer;
    } catch (error) {
      console.error('Failed to get customer:', error);
      throw error;
    }
  }

  /**
   * Update customer status (admin only)
   */
  static async updateCustomerStatus(id: string, status: string): Promise<Customer> {
    try {
      const { data } = await apolloClient.mutate({
        mutation: UPDATE_CUSTOMER_STATUS_MUTATION,
        variables: { id, status },
      });
      return data.updateCustomerStatus;
    } catch (error) {
      console.error('Failed to update customer status:', error);
      throw error;
    }
  }

  /**
   * Verify customer email (admin only)
   */
  static async verifyCustomerEmail(id: string): Promise<Customer> {
    try {
      const { data } = await apolloClient.mutate({
        mutation: VERIFY_CUSTOMER_EMAIL_MUTATION,
        variables: { id },
      });
      return data.verifyCustomerEmail;
    } catch (error) {
      console.error('Failed to verify customer email:', error);
      throw error;
    }
  }

  /**
   * Verify customer phone (admin only)
   */
  static async verifyCustomerPhone(id: string): Promise<Customer> {
    try {
      const { data } = await apolloClient.mutate({
        mutation: VERIFY_CUSTOMER_PHONE_MUTATION,
        variables: { id },
      });
      return data.verifyCustomerPhone;
    } catch (error) {
      console.error('Failed to verify customer phone:', error);
      throw error;
    }
  }

  /**
   * Store admin token (disabled for testing)
   */
  static storeAdminToken(token: string): void {
    // No-op since authentication is disabled
  }

  /**
   * Get stored admin token (disabled for testing)
   */
  static getStoredAdminToken(): string | null {
    // Always return null since authentication is disabled
    return null;
  }

  /**
   * Clear admin token (disabled for testing)
   */
  static clearAdminToken(): void {
    // No-op since authentication is disabled
  }

  /**
   * Check if admin is authenticated (always true for testing)
   */
  static isAuthenticated(): boolean {
    // Always return true since authentication is disabled
    return true;
  }

  /**
   * Get audit logs with pagination and filtering
   */
  static async getAuditLogs(page = 1, limit = 50, filter?: string): Promise<AuditLogsResponse['auditLogs']> {
    try {
      const { data } = await apolloClient.query<AuditLogsResponse>({
        query: GET_AUDIT_LOGS_QUERY,
        variables: { page, limit, filter },
      });
      return data.auditLogs;
    } catch (error) {
      console.error('Failed to get audit logs:', error);
      throw error;
    }
  }
}
