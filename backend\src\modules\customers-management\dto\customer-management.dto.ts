import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsEnum,
  IsArray,
  IsDateString,
  MinLength,
  MaxLength,

  Matches,
} from 'class-validator';

import { CustomerStatus, CustomerType } from '@prisma/client';

/**
 * DTO for creating a new customer
 */
export class CreateCustomerDto {
  @ApiProperty({ 
    description: 'Customer first name',
    example: '<PERSON>',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  firstName: string;

  @ApiProperty({ 
    description: 'Customer last name',
    example: 'Doe',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  lastName: string;

  @ApiProperty({ 
    description: 'Customer email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({ 
    description: 'Customer phone number in international format',
    example: '+1234567890'
  })
  @IsOptional()
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in international format (e.g., +1234567890)'
  })
  phone?: string;

  @ApiPropertyOptional({ 
    description: 'Customer date of birth',
    example: '1990-01-15'
  })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @ApiPropertyOptional({ 
    description: 'Company name for business customers',
    example: 'Acme Corporation',
    maxLength: 200
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  companyName?: string;

  @ApiPropertyOptional({ 
    description: 'Tax identification number',
    example: '12-3456789',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  taxId?: string;

  @ApiProperty({ 
    description: 'Customer type',
    enum: CustomerType,
    example: CustomerType.INDIVIDUAL
  })
  @IsEnum(CustomerType)
  type: CustomerType;

  @ApiPropertyOptional({ 
    description: 'Customer tags for categorization',
    example: ['premium', 'vip'],
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Additional notes about the customer',
    maxLength: 1000
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;

  @ApiPropertyOptional({ 
    description: 'External system ID for integration',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  externalId?: string;
}

/**
 * DTO for updating an existing customer
 */
export class UpdateCustomerDto {
  @ApiPropertyOptional({ 
    description: 'Customer first name',
    example: 'John',
    minLength: 1,
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  firstName?: string;

  @ApiPropertyOptional({ 
    description: 'Customer last name',
    example: 'Doe',
    minLength: 1,
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  lastName?: string;

  @ApiPropertyOptional({ 
    description: 'Customer email address',
    example: '<EMAIL>'
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ 
    description: 'Customer phone number in international format',
    example: '+1234567890'
  })
  @IsOptional()
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in international format (e.g., +1234567890)'
  })
  phone?: string;

  @ApiPropertyOptional({ 
    description: 'Customer date of birth',
    example: '1990-01-15'
  })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @ApiPropertyOptional({ 
    description: 'Company name for business customers',
    example: 'Acme Corporation',
    maxLength: 200
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  companyName?: string;

  @ApiPropertyOptional({ 
    description: 'Tax identification number',
    example: '12-3456789',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  taxId?: string;

  @ApiPropertyOptional({ 
    description: 'Customer type',
    enum: CustomerType,
    example: CustomerType.INDIVIDUAL
  })
  @IsOptional()
  @IsEnum(CustomerType)
  type?: CustomerType;

  @ApiPropertyOptional({ 
    description: 'Customer tags for categorization',
    example: ['premium', 'vip'],
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Additional notes about the customer',
    maxLength: 1000
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;

  @ApiPropertyOptional({ 
    description: 'External system ID for integration',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  externalId?: string;
}

/**
 * DTO for updating customer status
 */
export class UpdateCustomerStatusDto {
  @ApiProperty({ 
    description: 'New customer status',
    enum: CustomerStatus,
    example: CustomerStatus.ACTIVE
  })
  @IsEnum(CustomerStatus)
  status: CustomerStatus;

  @ApiPropertyOptional({ 
    description: 'Reason for status change',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;

  @ApiPropertyOptional({ 
    description: 'Additional notes for the status change',
    maxLength: 1000
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}
