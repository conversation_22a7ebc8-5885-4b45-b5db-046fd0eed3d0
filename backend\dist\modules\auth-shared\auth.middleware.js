"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthSharedMiddleware_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthSharedMiddleware = void 0;
exports.authMiddleware = authMiddleware;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
let AuthSharedMiddleware = AuthSharedMiddleware_1 = class AuthSharedMiddleware {
    authService;
    logger = new common_1.Logger(AuthSharedMiddleware_1.name);
    constructor(authService) {
        this.authService = authService;
    }
    async use(req, res, next) {
        try {
            const path = req.url || req.path || '';
            if (this.isPublicRoute(path)) {
                return next();
            }
            const cookies = req.cookies || {};
            try {
                const user = await this.authService.authenticateFromCookies(cookies);
                req.user = user;
                this.logger.debug(`User authenticated: ${user.email}`);
            }
            catch (error) {
                this.logger.debug('Authentication failed in middleware:', error.message);
            }
            next();
        }
        catch (error) {
            this.logger.error('Auth middleware error:', error);
            next();
        }
    }
    isPublicRoute(path) {
        if (!path)
            return false;
        const publicRoutes = [
            '/health',
            '/api/health',
            '/api/auth/login',
            '/api/auth/logout',
            '/api/auth/callback',
        ];
        return publicRoutes.some(route => path.startsWith(route));
    }
};
exports.AuthSharedMiddleware = AuthSharedMiddleware;
exports.AuthSharedMiddleware = AuthSharedMiddleware = AuthSharedMiddleware_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [auth_service_1.AuthSharedService])
], AuthSharedMiddleware);
function authMiddleware(authService) {
    return async (req, res, next) => {
        try {
            const cookies = req.cookies || {};
            try {
                const user = await authService.authenticateFromCookies(cookies);
                req.user = user;
            }
            catch (error) {
            }
            next();
        }
        catch (error) {
            console.error('Auth middleware error:', error);
            next();
        }
    };
}
//# sourceMappingURL=auth.middleware.js.map