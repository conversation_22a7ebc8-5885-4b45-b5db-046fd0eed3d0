"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WebhookController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const webhook_service_1 = require("./webhook.service");
let WebhookController = WebhookController_1 = class WebhookController {
    webhookService;
    logger = new common_1.Logger(WebhookController_1.name);
    constructor(webhookService) {
        this.webhookService = webhookService;
    }
    async handleWebhook(payload) {
        try {
            this.logger.log(`Received webhook: ${JSON.stringify(payload)}`);
            return await this.webhookService.processWebhook(payload);
        }
        catch (error) {
            if (error instanceof Error) {
                this.logger.error(`Error handling webhook: ${error.message}`, error.stack);
            }
            else {
                this.logger.error("Unknown error handling webhook");
            }
            throw new common_1.HttpException("Webhook processing failed", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async handleElavonWebhook(payload) {
        try {
            this.logger.log(`Received Elavon webhook: ${JSON.stringify(payload)}`);
            return await this.webhookService.processWebhook(payload);
        }
        catch (error) {
            if (error instanceof Error) {
                this.logger.error(`Error handling Elavon webhook: ${error.message}`, error.stack);
            }
            else {
                this.logger.error("Unknown error handling Elavon webhook");
            }
            throw new common_1.HttpException("Elavon webhook processing failed", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async handleTSEPWebhook(payload) {
        try {
            this.logger.log(`Received TSEP webhook: ${JSON.stringify(payload)}`);
            return await this.webhookService.processWebhook(payload);
        }
        catch (error) {
            if (error instanceof Error) {
                this.logger.error(`Error handling TSEP webhook: ${error.message}`, error.stack);
            }
            else {
                this.logger.error("Unknown error handling TSEP webhook");
            }
            throw new common_1.HttpException("TSEP webhook processing failed", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    healthCheck() {
        return { status: "ok" };
    }
};
exports.WebhookController = WebhookController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: "Handle generic webhook" }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: "object"
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Webhook processed successfully"
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookController.prototype, "handleWebhook", null);
__decorate([
    (0, common_1.Post)("elavon"),
    (0, swagger_1.ApiOperation)({ summary: "Handle Elavon payment webhook" }),
    (0, swagger_1.ApiBody)({
        description: "Elavon payment webhook payload",
        schema: {
            type: "object",
            properties: {
                reference_id: { type: "string" },
                currency: { type: "string" },
                amount: { type: "number" },
                provider_reference_id: { type: "string" },
                ssl_token: { type: "string" },
                transaction_id: { type: "string" },
                customer_name: { type: "string" },
                customer_id: { type: "string" },
                card_type: { type: "string" },
                status: { type: "string" },
                raw_response: { type: "object" }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Elavon webhook processed successfully"
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookController.prototype, "handleElavonWebhook", null);
__decorate([
    (0, common_1.Post)("tsep"),
    (0, swagger_1.ApiOperation)({ summary: "Handle TSEP payment webhook" }),
    (0, swagger_1.ApiBody)({
        description: "TSEP payment webhook payload",
        schema: {
            type: "object",
            properties: {
                reference_id: { type: "string" },
                currency: { type: "string" },
                amount: { type: "number" },
                provider_reference_id: { type: "string" },
                tsep_token: { type: "string" },
                transaction_id: { type: "string" },
                customer_name: { type: "string" },
                customer_id: { type: "string" },
                card_type: { type: "string" },
                status: { type: "string" },
                response_code: { type: "string" },
                raw_response: { type: "object" }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "TSEP webhook processed successfully"
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookController.prototype, "handleTSEPWebhook", null);
__decorate([
    (0, common_1.Get)("health"),
    (0, swagger_1.ApiOperation)({ summary: "Health check for webhook endpoint" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Webhook service is healthy",
        schema: {
            properties: {
                status: { type: "string", example: "ok" }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], WebhookController.prototype, "healthCheck", null);
exports.WebhookController = WebhookController = WebhookController_1 = __decorate([
    (0, swagger_1.ApiTags)("webhooks"),
    (0, common_1.Controller)("webhooks"),
    __metadata("design:paramtypes", [webhook_service_1.WebhookService])
], WebhookController);
//# sourceMappingURL=webhook.controller.js.map