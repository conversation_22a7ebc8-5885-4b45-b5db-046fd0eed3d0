import { Module } from '@nestjs/common';
import { CustomersAccessControlService } from './customers-access-control.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '../../config/config.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
  ],
  providers: [
    CustomersAccessControlService,
  ],
  exports: [
    CustomersAccessControlService,
  ],
})
export class CustomersAccessControlModule {}
