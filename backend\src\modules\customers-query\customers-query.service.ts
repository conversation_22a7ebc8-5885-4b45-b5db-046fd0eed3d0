import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Customer, Address, Contact, CustomerPreference, Prisma } from '@prisma/client';

export interface CustomerWithRelations extends Customer {
  addresses: Address[];
  contacts: Contact[];
  preferences: CustomerPreference[];
}

export interface CustomerFilterOptions {
  search?: string;
  status?: string;
  type?: string;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  isKycVerified?: boolean;
  tags?: string[];
  companyName?: string;
  country?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
}

export interface PaginationOptions {
  skip?: number;
  take?: number;
  orderBy?: Prisma.CustomerOrderByWithRelationInput;
}

export interface CustomerStatistics {
  total: number;
  active: number;
  inactive: number;
  suspended: number;
  pendingVerification: number;
  blocked: number;
  emailVerified: number;
  phoneVerified: number;
  kycVerified: number;
  individual: number;
  business: number;
  enterprise: number;
  recentlyCreated: number; // Last 30 days
  recentlyActive: number; // Last 30 days
}

@Injectable()
export class CustomersQueryService {
  private readonly logger = new Logger(CustomersQueryService.name);

  constructor(private readonly prisma: PrismaService) {}

  async findMany(
    filter?: CustomerFilterOptions,
    pagination?: PaginationOptions,
  ): Promise<CustomerWithRelations[]> {
    this.logger.log(`Finding customers with filter: ${JSON.stringify(filter)}`);

    const where: Prisma.CustomerWhereInput = {
      deletedAt: null, // Exclude soft-deleted customers
    };

    // Apply filters
    if (filter?.search) {
      where.OR = [
        { firstName: { contains: filter.search, mode: 'insensitive' } },
        { lastName: { contains: filter.search, mode: 'insensitive' } },
        { email: { contains: filter.search, mode: 'insensitive' } },
        { companyName: { contains: filter.search, mode: 'insensitive' } },
        { phone: { contains: filter.search, mode: 'insensitive' } },
        { taxId: { contains: filter.search, mode: 'insensitive' } },
      ];
    }

    if (filter?.status) {
      where.status = filter.status as any;
    }

    if (filter?.type) {
      where.type = filter.type as any;
    }

    if (filter?.isEmailVerified !== undefined) {
      where.isEmailVerified = filter.isEmailVerified;
    }

    if (filter?.isPhoneVerified !== undefined) {
      where.isPhoneVerified = filter.isPhoneVerified;
    }

    if (filter?.isKycVerified !== undefined) {
      where.isKycVerified = filter.isKycVerified;
    }

    if (filter?.tags && filter.tags.length > 0) {
      where.tags = {
        hasSome: filter.tags,
      };
    }

    if (filter?.companyName) {
      where.companyName = { contains: filter.companyName, mode: 'insensitive' };
    }

    if (filter?.country) {
      where.addresses = {
        some: {
          country: { contains: filter.country, mode: 'insensitive' },
        },
      };
    }

    if (filter?.createdAfter || filter?.createdBefore) {
      where.createdAt = {};
      if (filter?.createdAfter) {
        where.createdAt.gte = filter.createdAfter;
      }
      if (filter?.createdBefore) {
        where.createdAt.lte = filter.createdBefore;
      }
    }

    if (filter?.lastLoginAfter || filter?.lastLoginBefore) {
      where.lastLoginAt = {};
      if (filter?.lastLoginAfter) {
        where.lastLoginAt.gte = filter.lastLoginAfter;
      }
      if (filter?.lastLoginBefore) {
        where.lastLoginAt.lte = filter.lastLoginBefore;
      }
    }



    const customers = await this.prisma.customer.findMany({
      where,
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
      skip: pagination?.skip,
      take: pagination?.take,
      orderBy: pagination?.orderBy || { createdAt: 'desc' },
    });

    return customers;
  }

  async findById(id: string): Promise<CustomerWithRelations | null> {
    this.logger.log(`Finding customer by ID: ${id}`);

    const customer = await this.prisma.customer.findFirst({
      where: {
        id,
        deletedAt: null,
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10, // Limit audit logs for performance
        },
      },
    });

    if (!customer) {
      throw new NotFoundException(`Customer with ID ${id} not found`);
    }

    return customer;
  }

  async findByEmail(email: string): Promise<CustomerWithRelations | null> {
    this.logger.log(`Finding customer by email: ${email}`);

    const customer = await this.prisma.customer.findFirst({
      where: {
        email,
        deletedAt: null,
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
    });

    if (!customer) {
      return null;
    }

    return customer;
  }

  async count(filter?: CustomerFilterOptions): Promise<number> {
    this.logger.log(`Counting customers with filter: ${JSON.stringify(filter)}`);

    const where: Prisma.CustomerWhereInput = {
      deletedAt: null, // Exclude soft-deleted customers
    };

    // Apply same filters as findMany
    if (filter?.search) {
      where.OR = [
        { firstName: { contains: filter.search, mode: 'insensitive' } },
        { lastName: { contains: filter.search, mode: 'insensitive' } },
        { email: { contains: filter.search, mode: 'insensitive' } },
        { companyName: { contains: filter.search, mode: 'insensitive' } },
        { phone: { contains: filter.search, mode: 'insensitive' } },
        { taxId: { contains: filter.search, mode: 'insensitive' } },
      ];
    }

    if (filter?.status) {
      where.status = filter.status as any;
    }

    if (filter?.type) {
      where.type = filter.type as any;
    }

    if (filter?.isEmailVerified !== undefined) {
      where.isEmailVerified = filter.isEmailVerified;
    }

    if (filter?.isPhoneVerified !== undefined) {
      where.isPhoneVerified = filter.isPhoneVerified;
    }

    if (filter?.isKycVerified !== undefined) {
      where.isKycVerified = filter.isKycVerified;
    }

    if (filter?.tags && filter.tags.length > 0) {
      where.tags = {
        hasSome: filter.tags,
      };
    }

    if (filter?.companyName) {
      where.companyName = { contains: filter.companyName, mode: 'insensitive' };
    }

    if (filter?.country) {
      where.addresses = {
        some: {
          country: { contains: filter.country, mode: 'insensitive' },
        },
      };
    }

    if (filter?.createdAfter || filter?.createdBefore) {
      where.createdAt = {};
      if (filter?.createdAfter) {
        where.createdAt.gte = filter.createdAfter;
      }
      if (filter?.createdBefore) {
        where.createdAt.lte = filter.createdBefore;
      }
    }

    if (filter?.lastLoginAfter || filter?.lastLoginBefore) {
      where.lastLoginAt = {};
      if (filter?.lastLoginAfter) {
        where.lastLoginAt.gte = filter.lastLoginAfter;
      }
      if (filter?.lastLoginBefore) {
        where.lastLoginAt.lte = filter.lastLoginBefore;
      }
    }

    return this.prisma.customer.count({ where });
  }

  async getStatistics(): Promise<CustomerStatistics> {
    this.logger.log(`Getting customer statistics`);

    const baseWhere: Prisma.CustomerWhereInput = {
      deletedAt: null,
    };



    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [
      total,
      active,
      inactive,
      suspended,
      pendingVerification,
      blocked,
      emailVerified,
      phoneVerified,
      kycVerified,
      individual,
      business,
      enterprise,
      recentlyCreated,
      recentlyActive,
    ] = await Promise.all([
      this.prisma.customer.count({ where: baseWhere }),
      this.prisma.customer.count({ where: { ...baseWhere, status: 'ACTIVE' } }),
      this.prisma.customer.count({ where: { ...baseWhere, status: 'INACTIVE' } }),
      this.prisma.customer.count({ where: { ...baseWhere, status: 'SUSPENDED' } }),
      this.prisma.customer.count({ where: { ...baseWhere, status: 'PENDING_VERIFICATION' } }),
      this.prisma.customer.count({ where: { ...baseWhere, status: 'BLOCKED' } }),
      this.prisma.customer.count({ where: { ...baseWhere, isEmailVerified: true } }),
      this.prisma.customer.count({ where: { ...baseWhere, isPhoneVerified: true } }),
      this.prisma.customer.count({ where: { ...baseWhere, isKycVerified: true } }),
      this.prisma.customer.count({ where: { ...baseWhere, type: 'INDIVIDUAL' } }),
      this.prisma.customer.count({ where: { ...baseWhere, type: 'BUSINESS' } }),
      this.prisma.customer.count({ where: { ...baseWhere, type: 'ENTERPRISE' } }),
      this.prisma.customer.count({
        where: { ...baseWhere, createdAt: { gte: thirtyDaysAgo } },
      }),
      this.prisma.customer.count({
        where: { ...baseWhere, lastLoginAt: { gte: thirtyDaysAgo } },
      }),
    ]);

    return {
      total,
      active,
      inactive,
      suspended,
      pendingVerification,
      blocked,
      emailVerified,
      phoneVerified,
      kycVerified,
      individual,
      business,
      enterprise,
      recentlyCreated,
      recentlyActive,
    };
  }
}
