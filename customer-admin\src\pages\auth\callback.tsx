import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../auth';

export default function AuthCallback() {
  const router = useRouter();
  const { refreshAuth } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('🔄 Auth callback - refreshing authentication...');
        
        // Refresh authentication to check for new cookies
        await refreshAuth();
        
        // Get redirect URL from query params or default to dashboard
        const redirectTo = router.query.return_to as string || router.query.redirect as string || '/dashboard';
        
        console.log('✅ Auth callback - redirecting to:', redirectTo);
        
        // Redirect to the original page or dashboard
        router.replace(redirectTo);
      } catch (error) {
        console.error('❌ Auth callback error:', error);
        // Redirect to home page on error
        router.replace('/');
      }
    };

    handleCallback();
  }, [router, refreshAuth]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h1 className="text-xl font-semibold text-gray-900 mb-2">Completing Login...</h1>
        <p className="text-gray-600">Please wait while we redirect you back to the application.</p>
      </div>
    </div>
  );
}
