"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedCustomers = exports.CustomerSegmentMember = exports.CustomerSegment = exports.AuditLog = exports.CustomerPreference = exports.Contact = exports.Address = exports.Customer = void 0;
const graphql_1 = require("@nestjs/graphql");
const client_1 = require("@prisma/client");
(0, graphql_1.registerEnumType)(client_1.CustomerStatus, {
    name: 'CustomerStatus',
    description: 'Customer status enumeration',
});
(0, graphql_1.registerEnumType)(client_1.CustomerType, {
    name: 'CustomerType',
    description: 'Customer type enumeration',
});
(0, graphql_1.registerEnumType)(client_1.AddressType, {
    name: 'AddressType',
    description: 'Address type enumeration',
});
(0, graphql_1.registerEnumType)(client_1.ContactType, {
    name: 'ContactType',
    description: 'Contact type enumeration',
});
(0, graphql_1.registerEnumType)(client_1.PreferenceType, {
    name: 'PreferenceType',
    description: 'Preference type enumeration',
});
(0, graphql_1.registerEnumType)(client_1.AuditAction, {
    name: 'AuditAction',
    description: 'Audit action enumeration',
});
let Customer = class Customer {
    id;
    externalId;
    firstName;
    lastName;
    email;
    phone;
    dateOfBirth;
    companyName;
    taxId;
    businessType;
    status;
    type;
    isEmailVerified;
    isPhoneVerified;
    isKycVerified;
    get emailVerified() {
        return this.isEmailVerified;
    }
    get phoneVerified() {
        return this.isPhoneVerified;
    }
    get kycVerified() {
        return this.isKycVerified;
    }
    tags;
    notes;
    addresses;
    contacts;
    preferences;
    auditLogs;
    segmentMembers;
    createdAt;
    updatedAt;
    lastLoginAt;
    deletedAt;
};
exports.Customer = Customer;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], Customer.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "externalId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Customer.prototype, "firstName", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Customer.prototype, "lastName", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Customer.prototype, "email", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "phone", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "dateOfBirth", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "companyName", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "taxId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "businessType", void 0);
__decorate([
    (0, graphql_1.Field)(() => client_1.CustomerStatus),
    __metadata("design:type", String)
], Customer.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(() => client_1.CustomerType),
    __metadata("design:type", String)
], Customer.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Customer.prototype, "isEmailVerified", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Customer.prototype, "isPhoneVerified", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Customer.prototype, "isKycVerified", void 0);
__decorate([
    (0, graphql_1.Field)({ name: 'emailVerified' }),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], Customer.prototype, "emailVerified", null);
__decorate([
    (0, graphql_1.Field)({ name: 'phoneVerified' }),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], Customer.prototype, "phoneVerified", null);
__decorate([
    (0, graphql_1.Field)({ name: 'kycVerified' }),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], Customer.prototype, "kycVerified", null);
__decorate([
    (0, graphql_1.Field)(() => [String]),
    __metadata("design:type", Array)
], Customer.prototype, "tags", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Customer.prototype, "notes", void 0);
__decorate([
    (0, graphql_1.Field)(() => [Address]),
    __metadata("design:type", Array)
], Customer.prototype, "addresses", void 0);
__decorate([
    (0, graphql_1.Field)(() => [Contact]),
    __metadata("design:type", Array)
], Customer.prototype, "contacts", void 0);
__decorate([
    (0, graphql_1.Field)(() => [CustomerPreference]),
    __metadata("design:type", Array)
], Customer.prototype, "preferences", void 0);
__decorate([
    (0, graphql_1.Field)(() => [AuditLog], { nullable: true }),
    __metadata("design:type", Array)
], Customer.prototype, "auditLogs", void 0);
__decorate([
    (0, graphql_1.Field)(() => [CustomerSegmentMember], { nullable: true }),
    __metadata("design:type", Array)
], Customer.prototype, "segmentMembers", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Customer.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Customer.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "lastLoginAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "deletedAt", void 0);
exports.Customer = Customer = __decorate([
    (0, graphql_1.ObjectType)()
], Customer);
let Address = class Address {
    id;
    customerId;
    type;
    label;
    street1;
    street2;
    city;
    state;
    postalCode;
    country;
    latitude;
    longitude;
    isDefault;
    isVerified;
    customer;
    createdAt;
    updatedAt;
};
exports.Address = Address;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], Address.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Address.prototype, "customerId", void 0);
__decorate([
    (0, graphql_1.Field)(() => client_1.AddressType),
    __metadata("design:type", String)
], Address.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Address.prototype, "label", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Address.prototype, "street1", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Address.prototype, "street2", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Address.prototype, "city", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Address.prototype, "state", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Address.prototype, "postalCode", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Address.prototype, "country", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Number)
], Address.prototype, "latitude", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Number)
], Address.prototype, "longitude", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Address.prototype, "isDefault", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Address.prototype, "isVerified", void 0);
__decorate([
    (0, graphql_1.Field)(() => Customer),
    __metadata("design:type", Customer)
], Address.prototype, "customer", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Address.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Address.prototype, "updatedAt", void 0);
exports.Address = Address = __decorate([
    (0, graphql_1.ObjectType)()
], Address);
let Contact = class Contact {
    id;
    customerId;
    type;
    label;
    firstName;
    lastName;
    email;
    phone;
    relationship;
    isDefault;
    isVerified;
    customer;
    createdAt;
    updatedAt;
};
exports.Contact = Contact;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], Contact.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Contact.prototype, "customerId", void 0);
__decorate([
    (0, graphql_1.Field)(() => client_1.ContactType),
    __metadata("design:type", String)
], Contact.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Contact.prototype, "label", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Contact.prototype, "firstName", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Contact.prototype, "lastName", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Contact.prototype, "email", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Contact.prototype, "phone", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Contact.prototype, "relationship", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Contact.prototype, "isDefault", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Contact.prototype, "isVerified", void 0);
__decorate([
    (0, graphql_1.Field)(() => Customer),
    __metadata("design:type", Customer)
], Contact.prototype, "customer", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Contact.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Contact.prototype, "updatedAt", void 0);
exports.Contact = Contact = __decorate([
    (0, graphql_1.ObjectType)()
], Contact);
let CustomerPreference = class CustomerPreference {
    id;
    customerId;
    type;
    key;
    value;
    description;
    isActive;
    customer;
    createdAt;
    updatedAt;
};
exports.CustomerPreference = CustomerPreference;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], CustomerPreference.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CustomerPreference.prototype, "customerId", void 0);
__decorate([
    (0, graphql_1.Field)(() => client_1.PreferenceType),
    __metadata("design:type", String)
], CustomerPreference.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CustomerPreference.prototype, "key", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CustomerPreference.prototype, "value", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CustomerPreference.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], CustomerPreference.prototype, "isActive", void 0);
__decorate([
    (0, graphql_1.Field)(() => Customer),
    __metadata("design:type", Customer)
], CustomerPreference.prototype, "customer", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CustomerPreference.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CustomerPreference.prototype, "updatedAt", void 0);
exports.CustomerPreference = CustomerPreference = __decorate([
    (0, graphql_1.ObjectType)()
], CustomerPreference);
let AuditLog = class AuditLog {
    id;
    customerId;
    action;
    entity;
    entityId;
    description;
    customer;
    createdAt;
};
exports.AuditLog = AuditLog;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], AuditLog.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AuditLog.prototype, "customerId", void 0);
__decorate([
    (0, graphql_1.Field)(() => client_1.AuditAction),
    __metadata("design:type", String)
], AuditLog.prototype, "action", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AuditLog.prototype, "entity", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "entityId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(() => Customer),
    __metadata("design:type", Customer)
], AuditLog.prototype, "customer", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], AuditLog.prototype, "createdAt", void 0);
exports.AuditLog = AuditLog = __decorate([
    (0, graphql_1.ObjectType)()
], AuditLog);
let CustomerSegment = class CustomerSegment {
    id;
    name;
    description;
    isActive;
    isAutomatic;
    customers;
    createdAt;
    updatedAt;
};
exports.CustomerSegment = CustomerSegment;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], CustomerSegment.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CustomerSegment.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CustomerSegment.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], CustomerSegment.prototype, "isActive", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], CustomerSegment.prototype, "isAutomatic", void 0);
__decorate([
    (0, graphql_1.Field)(() => [CustomerSegmentMember]),
    __metadata("design:type", Array)
], CustomerSegment.prototype, "customers", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CustomerSegment.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CustomerSegment.prototype, "updatedAt", void 0);
exports.CustomerSegment = CustomerSegment = __decorate([
    (0, graphql_1.ObjectType)()
], CustomerSegment);
let CustomerSegmentMember = class CustomerSegmentMember {
    id;
    customerId;
    segmentId;
    assignedAt;
    assignedBy;
    isActive;
    customer;
    segment;
    createdAt;
    updatedAt;
};
exports.CustomerSegmentMember = CustomerSegmentMember;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], CustomerSegmentMember.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CustomerSegmentMember.prototype, "customerId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CustomerSegmentMember.prototype, "segmentId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CustomerSegmentMember.prototype, "assignedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CustomerSegmentMember.prototype, "assignedBy", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], CustomerSegmentMember.prototype, "isActive", void 0);
__decorate([
    (0, graphql_1.Field)(() => Customer),
    __metadata("design:type", Customer)
], CustomerSegmentMember.prototype, "customer", void 0);
__decorate([
    (0, graphql_1.Field)(() => CustomerSegment),
    __metadata("design:type", CustomerSegment)
], CustomerSegmentMember.prototype, "segment", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CustomerSegmentMember.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CustomerSegmentMember.prototype, "updatedAt", void 0);
exports.CustomerSegmentMember = CustomerSegmentMember = __decorate([
    (0, graphql_1.ObjectType)()
], CustomerSegmentMember);
let PaginatedCustomers = class PaginatedCustomers {
    data;
    total;
    page;
    limit;
};
exports.PaginatedCustomers = PaginatedCustomers;
__decorate([
    (0, graphql_1.Field)(() => [Customer]),
    __metadata("design:type", Array)
], PaginatedCustomers.prototype, "data", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], PaginatedCustomers.prototype, "total", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], PaginatedCustomers.prototype, "page", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], PaginatedCustomers.prototype, "limit", void 0);
exports.PaginatedCustomers = PaginatedCustomers = __decorate([
    (0, graphql_1.ObjectType)()
], PaginatedCustomers);
//# sourceMappingURL=customer.types.js.map