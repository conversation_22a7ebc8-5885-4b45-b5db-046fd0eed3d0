import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import { withAdminAuth } from '@/components/admin/withAdminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CustomerGraphQLService, Customer } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { 
  Search, 
  UserCheck, 
  UserX, 
  Mail,
  Phone,
  Shield,
  Loader2
} from 'lucide-react';

function CustomersPage() {
  const router = useRouter();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const limit = 10;

  useEffect(() => {
    loadCustomers();
  }, [currentPage, searchTerm]);

  const loadCustomers = async () => {
    try {
      setIsLoading(true);
      const data = await CustomerGraphQLService.getCustomers(
        currentPage, 
        limit, 
        searchTerm || undefined
      );
      setCustomers(data.data);
      setTotalCustomers(data.total);
    } catch (error) {
      console.error('Failed to load customers:', error);
      toast.error('Failed to load customers');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyEmail = async (customerId: string) => {
    try {
      await CustomerGraphQLService.verifyCustomerEmail(customerId);
      toast.success('Email verified successfully');
      loadCustomers(); // Refresh the list
    } catch (error) {
      console.error('Failed to verify email:', error);
      toast.error('Failed to verify email');
    }
  };

  const handleVerifyPhone = async (customerId: string) => {
    try {
      await CustomerGraphQLService.verifyCustomerPhone(customerId);
      toast.success('Phone verified successfully');
      loadCustomers(); // Refresh the list
    } catch (error) {
      console.error('Failed to verify phone:', error);
      toast.error('Failed to verify phone');
    }
  };

  const handleUpdateStatus = async (customerId: string, status: string) => {
    try {
      await CustomerGraphQLService.updateCustomerStatus(customerId, status);
      toast.success(`Customer status updated to ${status}`);
      loadCustomers(); // Refresh the list
    } catch (error) {
      console.error('Failed to update status:', error);
      toast.error('Failed to update customer status');
    }
  };

  const totalPages = Math.ceil(totalCustomers / limit);

  return (
    <AdminLayout title="Customer Management">
      <div className="space-y-6">
        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Search Customers</CardTitle>
            <CardDescription>
              Find customers by name, email, or phone number
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search customers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button onClick={loadCustomers}>
                Search
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Customers List */}
        <Card>
          <CardHeader>
            <CardTitle>Customers ({totalCustomers})</CardTitle>
            <CardDescription>
              Manage customer accounts and verification status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading customers...</span>
              </div>
            ) : customers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No customers found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {customers.map((customer) => (
                  <div key={customer.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div>
                          <h3 className="font-medium">
                            {customer.firstName} {customer.lastName}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            ID: {customer.id}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Mail className="h-4 w-4" />
                            <span>{customer.email}</span>
                            {customer.emailVerified ? (
                              <UserCheck className="h-4 w-4 text-green-600" />
                            ) : (
                              <UserX className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                          
                          {customer.phone && (
                            <div className="flex items-center space-x-1">
                              <Phone className="h-4 w-4" />
                              <span>{customer.phone}</span>
                              {customer.phoneVerified ? (
                                <UserCheck className="h-4 w-4 text-green-600" />
                              ) : (
                                <UserX className="h-4 w-4 text-red-600" />
                              )}
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            customer.status === 'active' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {customer.status}
                          </span>
                          {customer.kycVerified && (
                            <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                              KYC Verified
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col space-y-2">
                        {!customer.emailVerified && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleVerifyEmail(customer.id)}
                          >
                            <Mail className="h-4 w-4 mr-1" />
                            Verify Email
                          </Button>
                        )}
                        
                        {customer.phone && !customer.phoneVerified && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleVerifyPhone(customer.id)}
                          >
                            <Phone className="h-4 w-4 mr-1" />
                            Verify Phone
                          </Button>
                        )}

                        {customer.status !== 'active' && (
                          <Button
                            size="sm"
                            onClick={() => handleUpdateStatus(customer.id, 'active')}
                          >
                            <Shield className="h-4 w-4 mr-1" />
                            Activate
                          </Button>
                        )}

                        {customer.status === 'active' && (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleUpdateStatus(customer.id, 'suspended')}
                          >
                            Suspend
                          </Button>
                        )}

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/customers/${customer.id}`)}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages} ({totalCustomers} total customers)
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default withAdminAuth(CustomersPage);
