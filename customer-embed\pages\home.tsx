import React, { useState } from 'react';
import { GetServerSideProps } from 'next';
import Layout from '../src/components/Layout';
import AddCustomerModal from '../src/components/AddCustomerModal';

export default function Home() {
  const [isAddCustomerModalOpen, setIsAddCustomerModalOpen] = useState(false);

  return (
    <Layout>
      <div className="bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 py-24">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Customer Management
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Manage customer information and process orders efficiently.
            </p>
            <div className="mt-10">
              <button
                onClick={() => setIsAddCustomerModalOpen(true)}
                className="rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
              >
                Add Customer
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Add Customer Modal */}
      <AddCustomerModal
        isOpen={isAddCustomerModalOpen}
        onClose={() => setIsAddCustomerModalOpen(false)}
      />
    </Layout>
  );
}

// Disable static generation for this page since it uses auth context
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {},
  };
};
