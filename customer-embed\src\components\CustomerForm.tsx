import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { CustomerFormData, EmbedConfig } from '@/types/customer';
import { CustomerAPI } from '@/lib/api';
import { User, Mail, Phone, MapPin, CreditCard } from 'lucide-react';

interface CustomerFormProps {
  config?: EmbedConfig;
  onSubmit?: (data: CustomerFormData) => void;
  purchaseAmount?: number;
}

export default function CustomerForm({ config, onSubmit, purchaseAmount = 0 }: CustomerFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [taxInfo, setTaxInfo] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<CustomerFormData>();

  const watchedAddress = watch(['address', 'city', 'state', 'zipCode', 'country']);

  // Calculate tax when address changes
  React.useEffect(() => {
    const [address, city, state, zipCode, country] = watchedAddress;
    
    if (address && city && state && zipCode && country && purchaseAmount > 0) {
      calculateTax(address, city, state, zipCode, country);
    }
  }, [watchedAddress, purchaseAmount]);

  const calculateTax = async (address: string, city: string, state: string, zipCode: string, country: string) => {
    try {
      const taxResult = await CustomerAPI.calculateTax({
        amount: purchaseAmount,
        address: { street: address, city, state, zipCode, country },
      });
      
      setTaxInfo(taxResult);
      config?.onTaxCalculated?.(taxResult);
    } catch (error) {
      console.error('Tax calculation failed:', error);
    }
  };

  const onFormSubmit = async (data: CustomerFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const customer = await CustomerAPI.createCustomer(data);
      
      setSuccess(true);
      config?.onCustomerCreated?.(customer);
      onSubmit?.(data);
      
      // Reset form after successful submission
      setTimeout(() => {
        reset();
        setSuccess(false);
        setTaxInfo(null);
      }, 3000);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create customer';
      setError(errorMessage);
      config?.onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="max-w-md mx-auto p-6 bg-green-50 border border-green-200 rounded-lg">
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-green-800 mb-2">Customer Information Saved!</h3>
          <p className="text-green-600">Your information has been successfully recorded.</p>
          {taxInfo && (
            <div className="mt-4 p-3 bg-white rounded border">
              <p className="text-sm text-gray-600">Tax calculated: ${taxInfo.taxAmount.toFixed(2)}</p>
              <p className="text-sm font-medium text-gray-800">Total: ${taxInfo.totalAmount.toFixed(2)}</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Customer Information</h2>
        <p className="text-sm text-gray-600">Please provide your details for checkout</p>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="form-label">
              <User className="w-4 h-4 inline mr-1" />
              First Name
            </label>
            <input
              type="text"
              className="form-input"
              {...register('firstName', { required: 'First name is required' })}
            />
            {errors.firstName && <p className="form-error">{errors.firstName.message}</p>}
          </div>
          <div>
            <label className="form-label">Last Name</label>
            <input
              type="text"
              className="form-input"
              {...register('lastName', { required: 'Last name is required' })}
            />
            {errors.lastName && <p className="form-error">{errors.lastName.message}</p>}
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <label className="form-label">
            <Mail className="w-4 h-4 inline mr-1" />
            Email
          </label>
          <input
            type="email"
            className="form-input"
            {...register('email', { 
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address'
              }
            })}
          />
          {errors.email && <p className="form-error">{errors.email.message}</p>}
        </div>

        <div>
          <label className="form-label">
            <Phone className="w-4 h-4 inline mr-1" />
            Phone
          </label>
          <input
            type="tel"
            className="form-input"
            {...register('phone', { required: 'Phone number is required' })}
          />
          {errors.phone && <p className="form-error">{errors.phone.message}</p>}
        </div>

        {/* Address Information */}
        <div>
          <label className="form-label">
            <MapPin className="w-4 h-4 inline mr-1" />
            Address
          </label>
          <input
            type="text"
            className="form-input"
            {...register('address', { required: 'Address is required' })}
          />
          {errors.address && <p className="form-error">{errors.address.message}</p>}
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="form-label">City</label>
            <input
              type="text"
              className="form-input"
              {...register('city', { required: 'City is required' })}
            />
            {errors.city && <p className="form-error">{errors.city.message}</p>}
          </div>
          <div>
            <label className="form-label">State</label>
            <input
              type="text"
              className="form-input"
              {...register('state', { required: 'State is required' })}
            />
            {errors.state && <p className="form-error">{errors.state.message}</p>}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="form-label">ZIP Code</label>
            <input
              type="text"
              className="form-input"
              {...register('zipCode', { required: 'ZIP code is required' })}
            />
            {errors.zipCode && <p className="form-error">{errors.zipCode.message}</p>}
          </div>
          <div>
            <label className="form-label">Country</label>
            <select
              className="form-input"
              {...register('country', { required: 'Country is required' })}
            >
              <option value="">Select Country</option>
              <option value="US">United States</option>
              <option value="CA">Canada</option>
            </select>
            {errors.country && <p className="form-error">{errors.country.message}</p>}
          </div>
        </div>

        {/* Tax Information Display */}
        {taxInfo && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-800 mb-2">
              <CreditCard className="w-4 h-4 inline mr-1" />
              Tax Calculation
            </h4>
            <div className="text-sm text-blue-700 space-y-1">
              <p>Subtotal: ${purchaseAmount.toFixed(2)}</p>
              <p>Tax ({(taxInfo.taxRate * 100).toFixed(1)}%): ${taxInfo.taxAmount.toFixed(2)}</p>
              <p className="font-medium border-t border-blue-300 pt-1">
                Total: ${taxInfo.totalAmount.toFixed(2)}
              </p>
            </div>
          </div>
        )}

        <button
          type="submit"
          disabled={isLoading}
          className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            'Save Customer Information'
          )}
        </button>
      </form>
    </div>
  );
}
