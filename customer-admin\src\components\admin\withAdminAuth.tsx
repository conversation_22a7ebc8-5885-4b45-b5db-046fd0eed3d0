import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { Shield } from 'lucide-react';

interface WithAdminAuthOptions {
  redirectTo?: string;
}

export function withAdminAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAdminAuthOptions = {}
) {
  const { redirectTo = '/login' } = options;

  return function AdminProtectedComponent(props: P) {
    const { user, isLoading, isAuthenticated } = useAdminAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        router.push(redirectTo);
      }
    }, [isLoading, isAuthenticated, router]);

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <Shield className="h-8 w-8 animate-pulse text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Authenticating...</p>
          </div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return null; // Will redirect
    }

    return <WrappedComponent {...props} />;
  };
}
