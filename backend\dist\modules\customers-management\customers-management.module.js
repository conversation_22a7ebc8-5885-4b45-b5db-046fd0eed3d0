"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersManagementModule = void 0;
const common_1 = require("@nestjs/common");
const customers_management_service_1 = require("./customers-management.service");
const customers_management_controller_1 = require("./customers-management.controller");
const prisma_module_1 = require("../../prisma/prisma.module");
const config_module_1 = require("../../config/config.module");
const customers_query_module_1 = require("../customers-query/customers-query.module");
let CustomersManagementModule = class CustomersManagementModule {
};
exports.CustomersManagementModule = CustomersManagementModule;
exports.CustomersManagementModule = CustomersManagementModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            config_module_1.ConfigModule,
            customers_query_module_1.CustomersQueryModule,
        ],
        controllers: [
            customers_management_controller_1.CustomersManagementController,
        ],
        providers: [
            customers_management_service_1.CustomersManagementService,
        ],
        exports: [
            customers_management_service_1.CustomersManagementService,
        ],
    })
], CustomersManagementModule);
//# sourceMappingURL=customers-management.module.js.map