import React, { useState } from 'react';
import { CustomerResponse, TaxCalculationResponse } from '@/types/customer';

export default function Demo() {
  const [customerData, setCustomerData] = useState<CustomerResponse | null>(null);
  const [taxData, setTaxData] = useState<TaxCalculationResponse | null>(null);
  const [purchaseAmount, setPurchaseAmount] = useState(100);

  const handleMessage = (event: MessageEvent) => {
    switch (event.data.type) {
      case 'CUSTOMER_CREATED':
        setCustomerData(event.data.customer);
        break;
      case 'TAX_CALCULATED':
        setTaxData(event.data.tax);
        break;
      case 'CUSTOMER_ERROR':
        console.error('Customer form error:', event.data.error);
        break;
      case 'CUSTOMER_EMBED_READY':
        // Send configuration to iframe
        const iframe = document.getElementById('customer-iframe') as HTMLIFrameElement;
        if (iframe?.contentWindow) {
          iframe.contentWindow.postMessage({
            type: 'CUSTOMER_EMBED_CONFIG',
            config: {
              apiBaseUrl: 'http://localhost:3001/api/v1',
            },
            purchaseAmount,
          }, '*');
        }
        break;
    }
  };

  React.useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [purchaseAmount]);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Customer Embed Demo</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Iframe Container */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Embedded Customer Form</h2>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Purchase Amount: $
              </label>
              <input
                type="number"
                value={purchaseAmount}
                onChange={(e) => setPurchaseAmount(parseFloat(e.target.value) || 0)}
                className="w-32 px-3 py-2 border border-gray-300 rounded-md"
                min="0"
                step="0.01"
              />
            </div>
            
            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <iframe
                id="customer-iframe"
                src={`/`}
                width="100%"
                height="600"
                frameBorder="0"
                title="Customer Information Form"
                className="w-full"
              />
            </div>
          </div>

          {/* Data Display */}
          <div className="space-y-6">
            {/* Customer Data */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Customer Data</h3>
              {customerData ? (
                <div className="space-y-2 text-sm">
                  <p><strong>ID:</strong> {customerData.id}</p>
                  <p><strong>Name:</strong> {customerData.firstName} {customerData.lastName}</p>
                  <p><strong>Email:</strong> {customerData.email}</p>
                  <p><strong>Phone:</strong> {customerData.phone}</p>
                  <p><strong>Status:</strong> {customerData.status}</p>
                  <p><strong>Created:</strong> {new Date(customerData.createdAt).toLocaleString()}</p>
                </div>
              ) : (
                <p className="text-gray-500">No customer data yet</p>
              )}
            </div>

            {/* Tax Data */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Tax Calculation</h3>
              {taxData ? (
                <div className="space-y-2 text-sm">
                  <p><strong>Tax Rate:</strong> {(taxData.taxRate * 100).toFixed(2)}%</p>
                  <p><strong>Tax Amount:</strong> ${taxData.taxAmount.toFixed(2)}</p>
                  <p><strong>Total Amount:</strong> ${taxData.totalAmount.toFixed(2)}</p>
                  <div className="mt-3">
                    <p className="font-medium">Breakdown:</p>
                    <ul className="ml-4 space-y-1">
                      <li>State Tax: ${taxData.breakdown.stateTax.toFixed(2)}</li>
                      <li>Local Tax: ${taxData.breakdown.localTax.toFixed(2)}</li>
                      <li>Federal Tax: ${taxData.breakdown.federalTax.toFixed(2)}</li>
                    </ul>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No tax calculation yet</p>
              )}
            </div>

            {/* Integration Code */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Integration Code</h3>
              <pre className="bg-gray-100 p-4 rounded text-xs overflow-x-auto">
{`<!-- Embed in your checkout page -->
<iframe 
  src="http://localhost:3002/?amount=${purchaseAmount}"
  width="100%" 
  height="600"
  frameborder="0">
</iframe>

<script>
// Listen for customer data
window.addEventListener('message', (event) => {
  if (event.data.type === 'CUSTOMER_CREATED') {
    console.log('Customer:', event.data.customer);
    // Send to your backend
  }
  if (event.data.type === 'TAX_CALCULATED') {
    console.log('Tax:', event.data.tax);
    // Update your checkout total
  }
});
</script>`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
