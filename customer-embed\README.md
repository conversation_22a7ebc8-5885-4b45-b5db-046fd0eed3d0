# Customer Embed - Embeddable Customer Data Collection

This is an embeddable React component for collecting customer information during checkout, designed to integrate with the finance microservice.

## Features

- 🎯 **Embeddable**: Can be embedded as an iframe in any website
- 📊 **Tax Calculation**: Real-time tax calculation based on customer address
- 🔄 **Real-time Communication**: PostMessage API for parent-child communication
- 📱 **Responsive**: Mobile-friendly design
- 🎨 **Customizable**: Configurable styling and behavior
- 🔒 **Secure**: CORS-enabled for cross-origin embedding

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Set Environment Variables

```bash
cp .env.local.example .env.local
# Edit .env.local with your API endpoints
```

### 3. Run Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3002`

### 4. View Demo

Visit `http://localhost:3002/demo` to see the embedded form in action.

## Integration

### Basic Iframe Embedding

```html
<iframe 
  src="http://localhost:3002/?amount=100.00"
  width="100%" 
  height="600"
  frameborder="0">
</iframe>
```

### Advanced Integration with PostMessage

```javascript
// Listen for customer data
window.addEventListener('message', (event) => {
  switch (event.data.type) {
    case 'CUSTOMER_CREATED':
      console.log('Customer created:', event.data.customer);
      // Send customer data to your backend
      break;
      
    case 'TAX_CALCULATED':
      console.log('Tax calculated:', event.data.tax);
      // Update checkout total
      break;
      
    case 'CUSTOMER_ERROR':
      console.error('Error:', event.data.error);
      break;
  }
});

// Send configuration to iframe
iframe.contentWindow.postMessage({
  type: 'CUSTOMER_EMBED_CONFIG',
  config: {
    apiBaseUrl: 'https://your-api.com/api/v1',
    theme: {
      primaryColor: '#3b82f6',
      borderRadius: '8px'
    }
  },
  purchaseAmount: 100.00
}, '*');
```

## API Integration

The component integrates with your customer microservice API:

### Customer Creation
- **POST** `/api/v1/customers`
- Creates a new customer with address information

### Tax Calculation
- **POST** `/api/v1/tax/calculate`
- Calculates tax based on address and purchase amount

## Configuration Options

```typescript
interface EmbedConfig {
  apiBaseUrl?: string;
  onCustomerCreated?: (customer: CustomerResponse) => void;
  onTaxCalculated?: (tax: TaxCalculationResponse) => void;
  onError?: (error: string) => void;
  theme?: {
    primaryColor?: string;
    borderRadius?: string;
    fontFamily?: string;
  };
}
```

## Data Flow

1. **Customer fills form** → Real-time address validation
2. **Address complete** → Tax calculation via Numeral API
3. **Form submission** → Customer creation in database
4. **Success** → PostMessage to parent with customer & tax data
5. **Parent receives data** → Updates checkout and processes payment

## Finance Integration

This component is designed to work with the finance microservice:

1. **Finance frontend** embeds this iframe in checkout
2. **Customer data** is collected and validated
3. **Tax calculation** is performed automatically
4. **Customer & tax data** is sent to finance via PostMessage
5. **Finance processes payment** and updates customer record

## Kafka Integration (Optional)

For real-time updates, the component can publish events to Kafka:

- `customer.created` - When a customer is successfully created
- `tax.calculated` - When tax calculation is completed
- `form.error` - When form submission fails

## Deployment

### Build for Production

```bash
npm run build
npm run export
```

### Deploy as Static Files

The built files in the `out/` directory can be deployed to any static hosting service.

### Environment Variables for Production

```bash
NEXT_PUBLIC_API_BASE_URL=https://your-api.com/api/v1
NEXT_PUBLIC_TAX_SERVICE_URL=https://your-api.com/api/v1/tax
```

## Security Considerations

- CORS is configured to allow embedding from any domain
- No sensitive data is stored in the component
- All API calls go through your backend
- PostMessage communication is used for cross-origin data transfer

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License

Private - NGnair Customer Microservice
