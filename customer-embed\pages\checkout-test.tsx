import React from 'react';

export default function Checkout() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Checkout Demo - Customer Integration</h1>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">🛒 Checkout Page</h2>
          <p className="text-gray-600 mb-4">
            This is a simple checkout page to test the routing. The full checkout functionality
            will be implemented once the routing issue is resolved.
          </p>

          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">🔗 Integration Demo</h4>
            <p className="text-sm text-blue-800">
              This page demonstrates how the customer microservice integrates with a finance system.
              Customer data will be fetched from the seeded database and used for checkout processing.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
