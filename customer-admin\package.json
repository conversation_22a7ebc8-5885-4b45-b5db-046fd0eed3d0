{"name": "customer-management-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -H 0.0.0.0", "build": "next build", "start": "next start -H 0.0.0.0", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-webpack-plugin": "^13.0.0", "date-fns": "^4.1.0", "flowbite": "^3.1.2", "framer-motion": "^12.7.4", "graphql": "^16.11.0", "lucide-react": "^0.488.0", "next": "15.3.0", "next-svgr": "^0.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-plaid-link": "^4.0.0", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@shadcn/ui": "^0.0.4", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^3.3.0", "typescript": "^5"}}