/**
 * Debug utilities for API connections
 */

/**
 * Logs API requests with their details
 */
export const logApiRequest = (
  method: string,
  url: string,
  body?: unknown,
  headers?: Record<string, string>
): void => {
  console.log(`🔄 API ${method} Request to: ${url}`);
  
  if (headers) {
    console.log('Headers:', headers);
  }
  
  if (body) {
    console.log('Request Body:', body);
  }
};

/**
 * Logs API responses with their details
 */
export const logApiResponse = (
  url: string, 
  status: number, 
  data: unknown
): void => {
  console.log(`✅ API Response from ${url} - Status: ${status}`);
  console.log('Response Data:', data);
};

/**
 * Logs API errors with their details
 */
export const logApiError = (
  url: string, 
  error: unknown
): void => {
  console.error(`❌ API Error for ${url}:`, error);
  
  if (error instanceof Error) {
    console.error('Error Message:', error.message);
    console.error('Error Stack:', error.stack);
  }
};

/**
 * Tests connection to an API endpoint and returns the result
 */
export const testApiConnection = async (
  url: string,
  method = 'GET',
  headers: Record<string, string> = { 'Content-Type': 'application/json' }
): Promise<{
  ok: boolean;
  status?: number;
  statusText?: string;
  error?: string;
}> => {
  try {
    logApiRequest(method, url, undefined, headers);
    
    const response = await fetch(url, {
      method,
      headers,
    });
    
    return {
      ok: response.ok,
      status: response.status,
      statusText: response.statusText,
    };
  } catch (error) {
    logApiError(url, error);
    
    return {
      ok: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

/**
 * Get the API base URL from environment variables with a fallback
 */
export const getApiBaseUrl = (): string => {
  return process.env.OPEN_BANK_V2_API || 'http://localhost:3060/api/v1';
};

/**
 * Formats an OpenBank API endpoint with the proper path structure
 */
export const formatOpenbankEndpoint = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  
  // Ensure endpoint starts with a slash
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  // Ensure endpoint has /openbank/ prefix
  if (normalizedEndpoint.startsWith('/openbank/')) {
    return `${baseUrl}${normalizedEndpoint}`;
  } else {
    return `${baseUrl}/openbank${normalizedEndpoint}`;
  }
}; 