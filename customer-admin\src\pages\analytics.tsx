import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { withAdminAuth } from '@/components/admin/withAdminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CustomerGraphQLService } from '@/services/customer-graphql.service';
import { 
  Custom<PERSON>rea<PERSON>hart, 
  CustomBarChart, 
  CustomPieChart, 
  MultiLineChart,
  CHART_COLORS 
} from '@/components/ui/charts';
import { toast } from 'sonner';
import { 
  TrendingUp, 
  Users, 
  UserCheck, 
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react';
import { format, subDays, startOfDay } from 'date-fns';

interface AnalyticsData {
  totalCustomers: number;
  verifiedCustomers: number;
  activeCustomers: number;
  newCustomersToday: number;
  customerGrowth: Array<{ date: string; customers: number; verified: number }>;
  verificationStats: Array<{ name: string; value: number }>;
  statusDistribution: Array<{ name: string; value: number }>;
  monthlyTrends: Array<{ month: string; registrations: number; verifications: number }>;
}

function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalCustomers: 0,
    verifiedCustomers: 0,
    activeCustomers: 0,
    newCustomersToday: 0,
    customerGrowth: [],
    verificationStats: [],
    statusDistribution: [],
    monthlyTrends: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState(30); // days

  useEffect(() => {
    loadAnalytics();
  }, [dateRange]);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      
      // Fetch customers data
      const customersData = await CustomerGraphQLService.getCustomers(1, 1000);
      const customers = customersData.data;
      
      // Calculate basic stats
      const totalCustomers = customers.length;
      const verifiedCustomers = customers.filter(c => c.emailVerified && c.phoneVerified).length;
      const activeCustomers = customers.filter(c => c.status === 'active').length;
      
      // Calculate new customers today (mock data since we don't have real dates)
      const newCustomersToday = Math.floor(totalCustomers * 0.02);
      
      // Generate customer growth data (mock data for demonstration)
      const customerGrowth = Array.from({ length: dateRange }, (_, i) => {
        const date = format(subDays(new Date(), dateRange - i - 1), 'MMM dd');
        const baseCustomers = Math.floor(totalCustomers * (i + 1) / dateRange);
        const baseVerified = Math.floor(baseCustomers * 0.7);
        
        return {
          date,
          customers: baseCustomers + Math.floor(Math.random() * 10),
          verified: baseVerified + Math.floor(Math.random() * 5),
        };
      });
      
      // Verification statistics
      const emailVerified = customers.filter(c => c.emailVerified).length;
      const phoneVerified = customers.filter(c => c.phoneVerified).length;
      const kycVerified = customers.filter(c => c.kycVerified).length;
      
      const verificationStats = [
        { name: 'Email Verified', value: emailVerified },
        { name: 'Phone Verified', value: phoneVerified },
        { name: 'KYC Verified', value: kycVerified },
        { name: 'Unverified', value: totalCustomers - Math.max(emailVerified, phoneVerified) },
      ];
      
      // Status distribution
      const statusCounts = customers.reduce((acc, customer) => {
        acc[customer.status] = (acc[customer.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const statusDistribution = Object.entries(statusCounts).map(([name, value]) => ({
        name: name.charAt(0).toUpperCase() + name.slice(1),
        value,
      }));
      
      // Monthly trends (mock data)
      const monthlyTrends = [
        { month: 'Jan', registrations: 45, verifications: 32 },
        { month: 'Feb', registrations: 52, verifications: 41 },
        { month: 'Mar', registrations: 48, verifications: 38 },
        { month: 'Apr', registrations: 61, verifications: 49 },
        { month: 'May', registrations: 55, verifications: 44 },
        { month: 'Jun', registrations: 67, verifications: 53 },
      ];
      
      setAnalytics({
        totalCustomers,
        verifiedCustomers,
        activeCustomers,
        newCustomersToday,
        customerGrowth,
        verificationStats,
        statusDistribution,
        monthlyTrends,
      });
      
    } catch (error) {
      console.error('Failed to load analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    color = 'default' 
  }: {
    title: string;
    value: number | string;
    change?: string;
    icon: React.ComponentType<{ className?: string }>;
    color?: 'default' | 'success' | 'warning' | 'error';
  }) => {
    const colorClasses = {
      default: 'text-primary',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      error: 'text-red-600',
    };

    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <Icon className={`h-4 w-4 ${colorClasses[color]}`} />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{isLoading ? '...' : value}</div>
          {change && (
            <p className="text-xs text-muted-foreground mt-1">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              {change}
            </p>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <AdminLayout title="Analytics">
      <div className="space-y-6">
        {/* Header with controls */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Customer Analytics</h2>
            <p className="text-muted-foreground">
              Insights and trends for customer management
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(Number(e.target.value))}
              className="px-3 py-2 border rounded-md text-sm"
            >
              <option value={7}>Last 7 days</option>
              <option value={30}>Last 30 days</option>
              <option value={90}>Last 90 days</option>
            </select>
            <Button variant="outline" size="sm" onClick={loadAnalytics}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Customers"
            value={analytics.totalCustomers}
            change="+12% from last month"
            icon={Users}
          />
          <StatCard
            title="Verified Customers"
            value={analytics.verifiedCustomers}
            change="+8% from last month"
            icon={UserCheck}
            color="success"
          />
          <StatCard
            title="Active Customers"
            value={analytics.activeCustomers}
            change="+5% from last month"
            icon={TrendingUp}
            color="success"
          />
          <StatCard
            title="New Today"
            value={analytics.newCustomersToday}
            change="vs 3 yesterday"
            icon={Calendar}
          />
        </div>

        {/* Charts Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Customer Growth */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Growth</CardTitle>
              <CardDescription>
                Total and verified customers over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MultiLineChart
                data={analytics.customerGrowth}
                xKey="date"
                lines={[
                  { key: 'customers', name: 'Total Customers', color: CHART_COLORS.primary },
                  { key: 'verified', name: 'Verified Customers', color: CHART_COLORS.success },
                ]}
                height={300}
              />
            </CardContent>
          </Card>

          {/* Verification Status */}
          <Card>
            <CardHeader>
              <CardTitle>Verification Status</CardTitle>
              <CardDescription>
                Distribution of customer verification status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CustomPieChart
                data={analytics.verificationStats}
                dataKey="value"
                nameKey="name"
                height={300}
              />
            </CardContent>
          </Card>

          {/* Customer Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Status</CardTitle>
              <CardDescription>
                Distribution of customer account status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CustomBarChart
                data={analytics.statusDistribution}
                xKey="name"
                yKey="value"
                color={CHART_COLORS.primary}
                height={300}
              />
            </CardContent>
          </Card>

          {/* Monthly Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Trends</CardTitle>
              <CardDescription>
                Registration and verification trends by month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MultiLineChart
                data={analytics.monthlyTrends}
                xKey="month"
                lines={[
                  { key: 'registrations', name: 'Registrations', color: CHART_COLORS.primary },
                  { key: 'verifications', name: 'Verifications', color: CHART_COLORS.success },
                ]}
                height={300}
              />
            </CardContent>
          </Card>
        </div>

        {/* Detailed Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Detailed Metrics</CardTitle>
            <CardDescription>
              Comprehensive customer analytics breakdown
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <h4 className="font-medium">Verification Rates</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Email Verification:</span>
                    <span className="font-medium">
                      {analytics.totalCustomers > 0 
                        ? Math.round((analytics.verificationStats[0]?.value || 0) / analytics.totalCustomers * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Phone Verification:</span>
                    <span className="font-medium">
                      {analytics.totalCustomers > 0 
                        ? Math.round((analytics.verificationStats[1]?.value || 0) / analytics.totalCustomers * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>KYC Verification:</span>
                    <span className="font-medium">
                      {analytics.totalCustomers > 0 
                        ? Math.round((analytics.verificationStats[2]?.value || 0) / analytics.totalCustomers * 100)
                        : 0}%
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Activity Metrics</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Active Rate:</span>
                    <span className="font-medium">
                      {analytics.totalCustomers > 0 
                        ? Math.round(analytics.activeCustomers / analytics.totalCustomers * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Daily Growth:</span>
                    <span className="font-medium">+{analytics.newCustomersToday}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg. per Day:</span>
                    <span className="font-medium">
                      {Math.round(analytics.totalCustomers / 30)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Quality Metrics</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Fully Verified:</span>
                    <span className="font-medium">
                      {analytics.totalCustomers > 0 
                        ? Math.round(analytics.verifiedCustomers / analytics.totalCustomers * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Completion Rate:</span>
                    <span className="font-medium">87%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Quality Score:</span>
                    <span className="font-medium">8.5/10</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default withAdminAuth(AnalyticsPage);
