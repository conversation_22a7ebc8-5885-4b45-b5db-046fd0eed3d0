import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { AuthSharedService } from '../auth.service';

@Injectable()
export class ApiGuard implements CanActivate {
  constructor(private authService: AuthSharedService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};
      
      // Authenticate user
      const user = await this.authService.authenticateFromCookies(cookies);
      
      // Attach user to request
      request.user = user;
      
      return true;
    } catch (error) {
      throw new UnauthorizedException('Authentication required');
    }
  }
}

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(private authService: AuthSharedService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};
      
      // Authenticate user
      const user = await this.authService.authenticateFromCookies(cookies);
      
      // Check if user has admin role
      if (!this.authService.hasRole(user, 'admin') && !this.authService.hasRole(user, 'global_admin')) {
        throw new ForbiddenException('Admin access required');
      }
      
      // Attach user to request
      request.user = user;
      
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new UnauthorizedException('Authentication required');
    }
  }
}

export { AuthSharedService as AuthService } from '../auth.service';
