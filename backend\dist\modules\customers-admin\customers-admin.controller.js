"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CustomersAdminController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersAdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const customers_admin_service_1 = require("./customers-admin.service");
const customer_shared_dto_1 = require("../customers-shared/dto/customer-shared.dto");
let CustomersAdminController = CustomersAdminController_1 = class CustomersAdminController {
    customersAdminService;
    logger = new common_1.Logger(CustomersAdminController_1.name);
    constructor(customersAdminService) {
        this.customersAdminService = customersAdminService;
    }
    mapToResponseDto(customer) {
        return {
            ...customer,
            externalId: customer.externalId || undefined,
        };
    }
    async suspendCustomer(id) {
        this.logger.log(`Suspending customer ${id}`);
        const customer = await this.customersAdminService.suspendCustomer(id);
        return this.mapToResponseDto(customer);
    }
    async activateCustomer(id) {
        this.logger.log(`Activating customer ${id}`);
        const customer = await this.customersAdminService.activateCustomer(id);
        return this.mapToResponseDto(customer);
    }
    async adminUpdateCustomer(id, updateData) {
        this.logger.log(`Admin updating customer ${id}`);
        const customer = await this.customersAdminService.adminUpdateCustomer(id, updateData);
        return this.mapToResponseDto(customer);
    }
    async blockCustomer(id, blockData) {
        this.logger.log(`Blocking customer ${id}`);
        const customer = await this.customersAdminService.blockCustomer(id);
        return this.mapToResponseDto(customer);
    }
};
exports.CustomersAdminController = CustomersAdminController;
__decorate([
    (0, common_1.Post)(':id/suspend'),
    (0, swagger_1.ApiOperation)({
        summary: 'Suspend customer account (Admin only)',
        description: 'Suspend a customer account, preventing them from accessing services. This is an administrative action that requires elevated permissions.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer suspended successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
        examples: {
            success: {
                summary: 'Successful customer suspension',
                value: {
                    id: 'cmca6q7w40000l9015jo4lc5o',
                    email: '<EMAIL>',
                    firstName: 'John',
                    lastName: 'Doe',
                    status: 'SUSPENDED',
                    suspendedAt: '2024-01-15T16:00:00Z',
                    updatedAt: '2024-01-15T16:00:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to suspend customer',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Customer already suspended or not eligible for suspension',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersAdminController.prototype, "suspendCustomer", null);
__decorate([
    (0, common_1.Post)(':id/activate'),
    (0, swagger_1.ApiOperation)({
        summary: 'Activate customer account (Admin only)',
        description: 'Activate a suspended or inactive customer account, restoring their access to services. This is an administrative action that requires elevated permissions.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer activated successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
        examples: {
            success: {
                summary: 'Successful customer activation',
                value: {
                    id: 'cmca6q7w40000l9015jo4lc5o',
                    email: '<EMAIL>',
                    firstName: 'John',
                    lastName: 'Doe',
                    status: 'ACTIVE',
                    activatedAt: '2024-01-15T16:30:00Z',
                    updatedAt: '2024-01-15T16:30:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to activate customer',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Customer already active or not eligible for activation',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersAdminController.prototype, "activateCustomer", null);
__decorate([
    (0, common_1.Put)(':id/admin-update'),
    (0, swagger_1.ApiOperation)({
        summary: 'Admin update customer (Admin only)',
        description: 'Perform administrative updates on customer records with elevated permissions. This allows updating sensitive fields that regular updates cannot modify.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer updated successfully by admin',
        type: customer_shared_dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions for admin update',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomersAdminController.prototype, "adminUpdateCustomer", null);
__decorate([
    (0, common_1.Post)(':id/block'),
    (0, swagger_1.ApiOperation)({
        summary: 'Block customer account (Admin only)',
        description: 'Block a customer account for security or compliance reasons. This is a more severe action than suspension and requires elevated permissions.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer blocked successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to block customer',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomersAdminController.prototype, "blockCustomer", null);
exports.CustomersAdminController = CustomersAdminController = CustomersAdminController_1 = __decorate([
    (0, swagger_1.ApiTags)('customers-admin'),
    (0, common_1.Controller)('customers'),
    __metadata("design:paramtypes", [customers_admin_service_1.CustomersAdminService])
], CustomersAdminController);
//# sourceMappingURL=customers-admin.controller.js.map