"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerFilterDto = exports.CustomerListResponseDto = exports.PaginationDto = exports.CustomerResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
class CustomerResponseDto {
    id;
    externalId;
    firstName;
    lastName;
    email;
    phone;
    dateOfBirth;
    companyName;
    taxId;
    status;
    type;
    isEmailVerified;
    isPhoneVerified;
    isKycVerified;
    tags;
    notes;
    createdAt;
    updatedAt;
    deletedAt;
}
exports.CustomerResponseDto = CustomerResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer ID' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'External system ID' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "externalId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer first name' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer last name' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer email address' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Customer phone number' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Customer date of birth' }),
    __metadata("design:type", Date)
], CustomerResponseDto.prototype, "dateOfBirth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Company name for business customers' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tax identification number' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "taxId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer status',
        enum: client_1.CustomerStatus,
        example: client_1.CustomerStatus.ACTIVE
    }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer type',
        enum: client_1.CustomerType,
        example: client_1.CustomerType.INDIVIDUAL
    }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether email is verified' }),
    __metadata("design:type", Boolean)
], CustomerResponseDto.prototype, "isEmailVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether phone is verified' }),
    __metadata("design:type", Boolean)
], CustomerResponseDto.prototype, "isPhoneVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether KYC is verified' }),
    __metadata("design:type", Boolean)
], CustomerResponseDto.prototype, "isKycVerified", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Customer tags', type: [String] }),
    __metadata("design:type", Array)
], CustomerResponseDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Additional notes' }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation timestamp' }),
    __metadata("design:type", Date)
], CustomerResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update timestamp' }),
    __metadata("design:type", Date)
], CustomerResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Deletion timestamp (soft delete)' }),
    __metadata("design:type", Date)
], CustomerResponseDto.prototype, "deletedAt", void 0);
class PaginationDto {
    skip = 0;
    take = 20;
    sortBy;
    sortOrder = 'desc';
}
exports.PaginationDto = PaginationDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of records to skip',
        minimum: 0,
        default: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value, 10)),
    __metadata("design:type", Number)
], PaginationDto.prototype, "skip", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of records to take',
        minimum: 1,
        maximum: 100,
        default: 20
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value, 10)),
    __metadata("design:type", Number)
], PaginationDto.prototype, "take", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Field to sort by',
        example: 'createdAt'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PaginationDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order',
        enum: ['asc', 'desc'],
        default: 'desc'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PaginationDto.prototype, "sortOrder", void 0);
class CustomerListResponseDto {
    data;
    total;
    skip;
    take;
}
exports.CustomerListResponseDto = CustomerListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'List of customers', type: [CustomerResponseDto] }),
    __metadata("design:type", Array)
], CustomerListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of customers' }),
    __metadata("design:type", Number)
], CustomerListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of records skipped' }),
    __metadata("design:type", Number)
], CustomerListResponseDto.prototype, "skip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of records taken' }),
    __metadata("design:type", Number)
], CustomerListResponseDto.prototype, "take", void 0);
class CustomerFilterDto {
    search;
    status;
    type;
    isEmailVerified;
    isPhoneVerified;
    isKycVerified;
    tags;
    createdAfter;
    createdBefore;
    updatedAfter;
    updatedBefore;
}
exports.CustomerFilterDto = CustomerFilterDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search term for name, email, company, phone, or tax ID' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomerFilterDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by customer status',
        enum: client_1.CustomerStatus
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.CustomerStatus),
    __metadata("design:type", String)
], CustomerFilterDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by customer type',
        enum: client_1.CustomerType
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.CustomerType),
    __metadata("design:type", String)
], CustomerFilterDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by email verification status' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], CustomerFilterDto.prototype, "isEmailVerified", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by phone verification status' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], CustomerFilterDto.prototype, "isPhoneVerified", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by KYC verification status' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], CustomerFilterDto.prototype, "isKycVerified", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by customer tags', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CustomerFilterDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter customers created after this date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value) : undefined),
    __metadata("design:type", Date)
], CustomerFilterDto.prototype, "createdAfter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter customers created before this date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value) : undefined),
    __metadata("design:type", Date)
], CustomerFilterDto.prototype, "createdBefore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter customers updated after this date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value) : undefined),
    __metadata("design:type", Date)
], CustomerFilterDto.prototype, "updatedAfter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter customers updated before this date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value) : undefined),
    __metadata("design:type", Date)
], CustomerFilterDto.prototype, "updatedBefore", void 0);
//# sourceMappingURL=customer-shared.dto.js.map