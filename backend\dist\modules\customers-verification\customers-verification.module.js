"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersVerificationModule = void 0;
const common_1 = require("@nestjs/common");
const customers_verification_service_1 = require("./customers-verification.service");
const customers_verification_controller_1 = require("./customers-verification.controller");
const prisma_module_1 = require("../../prisma/prisma.module");
const config_module_1 = require("../../config/config.module");
const guards_module_1 = require("../shared/guards.module");
let CustomersVerificationModule = class CustomersVerificationModule {
};
exports.CustomersVerificationModule = CustomersVerificationModule;
exports.CustomersVerificationModule = CustomersVerificationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            config_module_1.ConfigModule,
            guards_module_1.SharedGuardsModule,
        ],
        controllers: [
            customers_verification_controller_1.CustomersVerificationController,
        ],
        providers: [
            customers_verification_service_1.CustomersVerificationService,
        ],
        exports: [
            customers_verification_service_1.CustomersVerificationService,
        ],
    })
], CustomersVerificationModule);
//# sourceMappingURL=customers-verification.module.js.map