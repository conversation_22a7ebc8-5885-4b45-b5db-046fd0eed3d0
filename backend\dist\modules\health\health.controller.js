"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_shared_1 = require("../auth-shared");
const health_service_1 = require("./health.service");
let HealthController = class HealthController {
    healthService;
    constructor(healthService) {
        this.healthService = healthService;
    }
    healthCheckPublic() {
        return this.healthService.checkHealth("public");
    }
    healthCheckUser(user) {
        if (!user) {
            throw new common_1.UnauthorizedException("Authentication required");
        }
        return this.healthService.checkHealth("user");
    }
    healthCheckAdmin(user) {
        if (!user) {
            throw new common_1.UnauthorizedException("Authentication required");
        }
        if (!user.isAdmin) {
            throw new common_1.ForbiddenException("Access denied: Admin privileges required");
        }
        return this.healthService.checkHealth("admin");
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)("public"),
    (0, swagger_1.ApiOperation)({ summary: "Public health check endpoint" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Service health status - Public access",
        schema: {
            properties: {
                status: { type: "string", example: "ok" },
                timestamp: { type: "string", format: "date-time" },
                access: { type: "string", example: "public" },
                message: { type: "string", example: "Basic health check" }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "healthCheckPublic", null);
__decorate([
    (0, common_1.Get)("user"),
    (0, common_1.UseGuards)(auth_shared_1.ApiGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "User health check endpoint - Requires user authentication" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Service health status - User access",
        schema: {
            properties: {
                status: { type: "string", example: "ok" },
                timestamp: { type: "string", format: "date-time" },
                access: { type: "string", example: "user" },
                message: { type: "string", example: "Authenticated health check" }
            }
        }
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: "User is not authenticated",
        schema: {
            properties: {
                statusCode: { type: "number", example: 401 },
                message: { type: "string", example: "Unauthorized access" }
            }
        }
    }),
    __param(0, (0, auth_shared_1.getCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "healthCheckUser", null);
__decorate([
    (0, common_1.Get)("admin"),
    (0, common_1.UseGuards)(auth_shared_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Admin health check endpoint - Requires admin authentication" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Service health status - Admin access",
        schema: {
            properties: {
                status: { type: "string", example: "ok" },
                timestamp: { type: "string", format: "date-time" },
                access: { type: "string", example: "admin" },
                message: { type: "string", example: "Full system health check with details" },
                details: {
                    type: "object",
                    properties: {
                        database: { type: "boolean" },
                        cache: { type: "boolean" },
                        auth: { type: "boolean" }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: "User is not authenticated",
        schema: {
            properties: {
                statusCode: { type: "number", example: 401 },
                message: { type: "string", example: "Unauthorized access" }
            }
        }
    }),
    (0, swagger_1.ApiForbiddenResponse)({
        description: "User is not an admin",
        schema: {
            properties: {
                statusCode: { type: "number", example: 403 },
                message: { type: "string", example: "Access denied: Admin privileges required" }
            }
        }
    }),
    __param(0, (0, auth_shared_1.getCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "healthCheckAdmin", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)("health"),
    (0, common_1.Controller)("health"),
    __metadata("design:paramtypes", [health_service_1.HealthService])
], HealthController);
//# sourceMappingURL=health.controller.js.map