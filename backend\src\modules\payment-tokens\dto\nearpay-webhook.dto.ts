import { IsString, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, IsEnum, IsDateString, <PERSON>idateNested, IsObject } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export enum NearPayTransactionStatus {
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'
}

export enum NearPayTransactionType {
  SALE = 'SALE',
  REFUND = 'REFUND',
  VOID = 'VOID',
  AUTH = 'AUTH',
  CAPTURE = 'CAPTURE'
}

export class NearPayCardData {
  @ApiProperty({ description: 'Last 4 digits of the card' })
  @IsString()
  last4: string;

  @ApiProperty({ description: 'Card brand (Visa, Mastercard, etc.)' })
  @IsString()
  brand: string;

  @ApiProperty({ description: 'Card type (Credit, Debit, etc.)', required: false })
  @IsOptional()
  @IsString()
  cardType?: string;

  @ApiProperty({ description: 'Card expiry month', required: false })
  @IsOptional()
  @IsString()
  expiryMonth?: string;

  @ApiProperty({ description: 'Card expiry year', required: false })
  @IsOptional()
  @IsString()
  expiryYear?: string;

  @ApiProperty({ description: 'Cardholder name', required: false })
  @IsOptional()
  @IsString()
  cardholderName?: string;
}

export class NearPayMerchantData {
  @ApiProperty({ description: 'Merchant ID' })
  @IsString()
  merchantId: string;

  @ApiProperty({ description: 'Terminal ID' })
  @IsString()
  terminalId: string;

  @ApiProperty({ description: 'Merchant name', required: false })
  @IsOptional()
  @IsString()
  merchantName?: string;
}

export class NearPayWebhookDto {
  @ApiProperty({ description: 'Unique transaction ID from NearPay' })
  @IsString()
  transactionId: string;

  @ApiProperty({ description: 'Transaction reference number', required: false })
  @IsOptional()
  @IsString()
  referenceNumber?: string;

  @ApiProperty({ description: 'Transaction amount in cents' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Currency code (e.g., USD, SAR)' })
  @IsString()
  currency: string;

  @ApiProperty({ description: 'Transaction status', enum: NearPayTransactionStatus })
  @IsEnum(NearPayTransactionStatus)
  status: NearPayTransactionStatus;

  @ApiProperty({ description: 'Transaction type', enum: NearPayTransactionType })
  @IsEnum(NearPayTransactionType)
  type: NearPayTransactionType;

  @ApiProperty({ description: 'Authorization code', required: false })
  @IsOptional()
  @IsString()
  authCode?: string;

  @ApiProperty({ description: 'Card information' })
  @ValidateNested()
  @Type(() => NearPayCardData)
  card: NearPayCardData;

  @ApiProperty({ description: 'Merchant information' })
  @ValidateNested()
  @Type(() => NearPayMerchantData)
  merchant: NearPayMerchantData;

  @ApiProperty({
    description: 'Transaction timestamp in ISO 8601 format',
    example: '2024-01-15T10:30:00.000Z'
  })
  @IsDateString({}, { message: 'timestamp must be a valid ISO 8601 date string' })
  @Transform(({ value }) => {
    // Handle various timestamp formats
    if (typeof value === 'string') {
      // If it's already a valid ISO string, return as is
      if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/)) {
        return value.endsWith('Z') ? value : value + 'Z';
      }
      // Try to parse and convert to ISO string
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return date.toISOString();
      }
    }
    return value;
  })
  timestamp: string;

  @ApiProperty({ description: 'Customer identifier (email, phone, or external ID)', required: false })
  @IsOptional()
  @IsString()
  customerId?: string;

  @ApiProperty({ description: 'Customer email', required: false })
  @IsOptional()
  @IsString()
  customerEmail?: string;

  @ApiProperty({ description: 'Customer phone', required: false })
  @IsOptional()
  @IsString()
  customerPhone?: string;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Webhook signature for verification', required: false })
  @IsOptional()
  @IsString()
  signature?: string;

  @ApiProperty({ description: 'Webhook event type', required: false })
  @IsOptional()
  @IsString()
  eventType?: string;
}

export class PaymentTokenResponseDto {
  @ApiProperty({ description: 'Token ID' })
  id: string;

  @ApiProperty({ description: 'Customer ID' })
  customerId: string;

  @ApiProperty({ description: 'Payment provider' })
  paymentProvider: string;

  @ApiProperty({ description: 'Token type' })
  tokenType: string;

  @ApiProperty({ description: 'Token status' })
  status: string;

  @ApiProperty({ description: 'Masked payment info', required: false })
  maskedInfo?: string;

  @ApiProperty({ description: 'Payment brand', required: false })
  paymentBrand?: string;

  @ApiProperty({ description: 'Expiration date', required: false })
  expiresAt?: Date;

  @ApiProperty({ description: 'Last used date', required: false })
  lastUsedAt?: Date;

  @ApiProperty({ description: 'Usage count' })
  usageCount: number;

  @ApiProperty({ description: 'Created date' })
  createdAt: Date;
}
