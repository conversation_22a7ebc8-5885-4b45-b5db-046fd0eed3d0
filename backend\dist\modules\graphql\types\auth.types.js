"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecodeJwtResponse = exports.DecodeJwtInput = exports.DecryptTokenResponse = exports.DecryptTokenInput = exports.AuthStatus = exports.User = void 0;
const graphql_1 = require("@nestjs/graphql");
let User = class User {
    id;
    email;
    username;
    firstName;
    lastName;
    role;
    permissions;
    createdAt;
    updatedAt;
};
exports.User = User;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "firstName", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    (0, graphql_1.Field)(() => [String], { nullable: true }),
    __metadata("design:type", Array)
], User.prototype, "permissions", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], User.prototype, "updatedAt", void 0);
exports.User = User = __decorate([
    (0, graphql_1.ObjectType)()
], User);
let AuthStatus = class AuthStatus {
    status;
    service;
    timestamp;
    version;
};
exports.AuthStatus = AuthStatus;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AuthStatus.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AuthStatus.prototype, "service", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AuthStatus.prototype, "timestamp", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AuthStatus.prototype, "version", void 0);
exports.AuthStatus = AuthStatus = __decorate([
    (0, graphql_1.ObjectType)()
], AuthStatus);
let DecryptTokenInput = class DecryptTokenInput {
    token;
};
exports.DecryptTokenInput = DecryptTokenInput;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DecryptTokenInput.prototype, "token", void 0);
exports.DecryptTokenInput = DecryptTokenInput = __decorate([
    (0, graphql_1.InputType)()
], DecryptTokenInput);
let DecryptTokenResponse = class DecryptTokenResponse {
    decryptedToken;
    tokenType;
    success;
    error;
};
exports.DecryptTokenResponse = DecryptTokenResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DecryptTokenResponse.prototype, "decryptedToken", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DecryptTokenResponse.prototype, "tokenType", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], DecryptTokenResponse.prototype, "success", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DecryptTokenResponse.prototype, "error", void 0);
exports.DecryptTokenResponse = DecryptTokenResponse = __decorate([
    (0, graphql_1.ObjectType)()
], DecryptTokenResponse);
let DecodeJwtInput = class DecodeJwtInput {
    jwt;
};
exports.DecodeJwtInput = DecodeJwtInput;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DecodeJwtInput.prototype, "jwt", void 0);
exports.DecodeJwtInput = DecodeJwtInput = __decorate([
    (0, graphql_1.InputType)()
], DecodeJwtInput);
let DecodeJwtResponse = class DecodeJwtResponse {
    header;
    payload;
    signature;
    success;
    error;
};
exports.DecodeJwtResponse = DecodeJwtResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DecodeJwtResponse.prototype, "header", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DecodeJwtResponse.prototype, "payload", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DecodeJwtResponse.prototype, "signature", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], DecodeJwtResponse.prototype, "success", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DecodeJwtResponse.prototype, "error", void 0);
exports.DecodeJwtResponse = DecodeJwtResponse = __decorate([
    (0, graphql_1.ObjectType)()
], DecodeJwtResponse);
//# sourceMappingURL=auth.types.js.map