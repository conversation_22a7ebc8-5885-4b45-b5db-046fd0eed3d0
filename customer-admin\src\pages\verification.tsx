import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { withAdminAuth } from '@/components/admin/withAdminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CustomerGraphQLService, Customer } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { 
  Mail, 
  Phone, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Clock,
  Search,
  Filter,
  UserCheck,
  AlertTriangle,
  Loader2
} from 'lucide-react';

interface VerificationStats {
  pendingEmail: number;
  pendingPhone: number;
  pendingKyc: number;
  totalPending: number;
}

function VerificationPage() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [stats, setStats] = useState<VerificationStats>({
    pendingEmail: 0,
    pendingPhone: 0,
    pendingKyc: 0,
    totalPending: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'email' | 'phone' | 'kyc'>('all');
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadVerificationData();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm, filterType]);

  const loadVerificationData = async () => {
    try {
      setIsLoading(true);
      const data = await CustomerGraphQLService.getCustomers(1, 1000);
      const allCustomers = data.data;
      
      // Filter customers who need verification
      const pendingCustomers = allCustomers.filter(customer => 
        !customer.emailVerified || !customer.phoneVerified || !customer.kycVerified
      );
      
      // Calculate stats
      const pendingEmail = allCustomers.filter(c => !c.emailVerified).length;
      const pendingPhone = allCustomers.filter(c => !c.phoneVerified).length;
      const pendingKyc = allCustomers.filter(c => !c.kycVerified).length;
      
      setCustomers(pendingCustomers);
      setStats({
        pendingEmail,
        pendingPhone,
        pendingKyc,
        totalPending: pendingCustomers.length,
      });
      
    } catch (error) {
      console.error('Failed to load verification data:', error);
      toast.error('Failed to load verification data');
    } finally {
      setIsLoading(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (customer.phone && customer.phone.includes(searchTerm))
      );
    }
    
    // Apply type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(customer => {
        switch (filterType) {
          case 'email':
            return !customer.emailVerified;
          case 'phone':
            return !customer.phoneVerified;
          case 'kyc':
            return !customer.kycVerified;
          default:
            return true;
        }
      });
    }
    
    setFilteredCustomers(filtered);
  };

  const handleVerifyEmail = async (customerId: string) => {
    setProcessingIds(prev => new Set(prev).add(customerId));
    try {
      await CustomerGraphQLService.verifyCustomerEmail(customerId);
      toast.success('Email verified successfully');
      loadVerificationData();
    } catch (error) {
      console.error('Failed to verify email:', error);
      toast.error('Failed to verify email');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(customerId);
        return newSet;
      });
    }
  };

  const handleVerifyPhone = async (customerId: string) => {
    setProcessingIds(prev => new Set(prev).add(customerId));
    try {
      await CustomerGraphQLService.verifyCustomerPhone(customerId);
      toast.success('Phone verified successfully');
      loadVerificationData();
    } catch (error) {
      console.error('Failed to verify phone:', error);
      toast.error('Failed to verify phone');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(customerId);
        return newSet;
      });
    }
  };

  const StatCard = ({ 
    title, 
    count, 
    icon: Icon, 
    color = 'default',
    onClick 
  }: {
    title: string;
    count: number;
    icon: React.ComponentType<{ className?: string }>;
    color?: 'default' | 'warning' | 'error';
    onClick?: () => void;
  }) => {
    const colorClasses = {
      default: 'text-blue-600 bg-blue-50 border-blue-200',
      warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
      error: 'text-red-600 bg-red-50 border-red-200',
    };

    return (
      <Card 
        className={`cursor-pointer transition-colors hover:shadow-md ${onClick ? 'hover:bg-accent' : ''}`}
        onClick={onClick}
      >
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-full ${colorClasses[color]}`}>
              <Icon className="h-6 w-6" />
            </div>
            <div>
              <p className="text-2xl font-bold">{count}</p>
              <p className="text-sm text-muted-foreground">{title}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <AdminLayout title="Verification Management">
      <div className="space-y-6">
        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Pending"
            count={stats.totalPending}
            icon={Clock}
            color="warning"
            onClick={() => setFilterType('all')}
          />
          <StatCard
            title="Email Pending"
            count={stats.pendingEmail}
            icon={Mail}
            color="error"
            onClick={() => setFilterType('email')}
          />
          <StatCard
            title="Phone Pending"
            count={stats.pendingPhone}
            icon={Phone}
            color="error"
            onClick={() => setFilterType('phone')}
          />
          <StatCard
            title="KYC Pending"
            count={stats.pendingKyc}
            icon={Shield}
            color="warning"
            onClick={() => setFilterType('kyc')}
          />
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
            <CardDescription>
              Find customers requiring verification
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as any)}
                  className="px-3 py-2 border rounded-md text-sm"
                >
                  <option value="all">All Pending</option>
                  <option value="email">Email Pending</option>
                  <option value="phone">Phone Pending</option>
                  <option value="kyc">KYC Pending</option>
                </select>
                <Button variant="outline" onClick={loadVerificationData}>
                  <Filter className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Verification Queue */}
        <Card>
          <CardHeader>
            <CardTitle>Verification Queue ({filteredCustomers.length})</CardTitle>
            <CardDescription>
              Customers requiring verification actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading verification queue...</span>
              </div>
            ) : filteredCustomers.length === 0 ? (
              <div className="text-center py-8">
                <UserCheck className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <p className="text-lg font-medium">All caught up!</p>
                <p className="text-muted-foreground">No pending verifications found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredCustomers.map((customer) => {
                  const isProcessing = processingIds.has(customer.id);
                  
                  return (
                    <div key={customer.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-3">
                          <div>
                            <h3 className="font-medium">
                              {customer.firstName} {customer.lastName}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              ID: {customer.id}
                            </p>
                          </div>
                          
                          {/* Verification Status */}
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4" />
                              <span className="text-sm">{customer.email}</span>
                              {customer.emailVerified ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-600" />
                              )}
                              {!customer.emailVerified && (
                                <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                  Pending
                                </span>
                              )}
                            </div>
                            
                            {customer.phone && (
                              <div className="flex items-center space-x-2">
                                <Phone className="h-4 w-4" />
                                <span className="text-sm">{customer.phone}</span>
                                {customer.phoneVerified ? (
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-600" />
                                )}
                                {!customer.phoneVerified && (
                                  <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                    Pending
                                  </span>
                                )}
                              </div>
                            )}
                            
                            <div className="flex items-center space-x-2">
                              <Shield className="h-4 w-4" />
                              <span className="text-sm">KYC Verification</span>
                              {customer.kycVerified ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : (
                                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                              )}
                              {!customer.kycVerified && (
                                <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                  Pending
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col space-y-2">
                          {!customer.emailVerified && (
                            <Button
                              size="sm"
                              onClick={() => handleVerifyEmail(customer.id)}
                              disabled={isProcessing}
                            >
                              {isProcessing ? (
                                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                              ) : (
                                <Mail className="h-4 w-4 mr-1" />
                              )}
                              Verify Email
                            </Button>
                          )}
                          
                          {customer.phone && !customer.phoneVerified && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleVerifyPhone(customer.id)}
                              disabled={isProcessing}
                            >
                              {isProcessing ? (
                                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                              ) : (
                                <Phone className="h-4 w-4 mr-1" />
                              )}
                              Verify Phone
                            </Button>
                          )}
                          
                          {!customer.kycVerified && (
                            <Button
                              size="sm"
                              variant="secondary"
                              disabled
                            >
                              <Shield className="h-4 w-4 mr-1" />
                              Review KYC
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default withAdminAuth(VerificationPage);
