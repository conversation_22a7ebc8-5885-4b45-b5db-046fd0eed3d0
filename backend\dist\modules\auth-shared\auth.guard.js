"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthSharedGuard = exports.Public = exports.Permissions = exports.Roles = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const auth_service_1 = require("./auth.service");
const Roles = (roles) => {
    return (target, propertyKey, descriptor) => {
        Reflect.defineMetadata('roles', roles, target, propertyKey || '');
    };
};
exports.Roles = Roles;
const Permissions = (permissions) => {
    return (target, propertyKey, descriptor) => {
        Reflect.defineMetadata('permissions', permissions, target, propertyKey || '');
    };
};
exports.Permissions = Permissions;
const Public = () => {
    return (target, propertyKey, descriptor) => {
        Reflect.defineMetadata('isPublic', true, target, propertyKey || '');
    };
};
exports.Public = Public;
let AuthSharedGuard = class AuthSharedGuard {
    authService;
    reflector;
    constructor(authService, reflector) {
        this.authService = authService;
        this.reflector = reflector;
    }
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride('isPublic', [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        try {
            const cookies = request.cookies || {};
            const user = await this.authService.authenticateFromCookies(cookies);
            request.user = user;
            const requiredRoles = this.reflector.getAllAndOverride('roles', [
                context.getHandler(),
                context.getClass(),
            ]);
            if (requiredRoles && requiredRoles.length > 0) {
                const hasRole = requiredRoles.some(role => this.authService.hasRole(user, role));
                if (!hasRole) {
                    throw new common_1.ForbiddenException(`Required role: ${requiredRoles.join(' or ')}`);
                }
            }
            const requiredPermissions = this.reflector.getAllAndOverride('permissions', [
                context.getHandler(),
                context.getClass(),
            ]);
            if (requiredPermissions && requiredPermissions.length > 0) {
                const hasPermissions = this.authService.hasPermissions(user, requiredPermissions);
                if (!hasPermissions) {
                    throw new common_1.ForbiddenException(`Required permissions: ${requiredPermissions.join(', ')}`);
                }
            }
            return true;
        }
        catch (error) {
            if (error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.UnauthorizedException('Authentication required');
        }
    }
};
exports.AuthSharedGuard = AuthSharedGuard;
exports.AuthSharedGuard = AuthSharedGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [auth_service_1.AuthSharedService,
        core_1.Reflector])
], AuthSharedGuard);
//# sourceMappingURL=auth.guard.js.map