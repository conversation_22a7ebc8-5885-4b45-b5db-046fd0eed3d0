import {
  Controller,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Logger,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { CustomersManagementService, CustomerWithRelations } from './customers-management.service';
import {
  CreateCustomerDto,
  UpdateCustomerDto,
} from './dto/customer-management.dto';
import { CustomerResponseDto } from '../customers-shared/dto/customer-shared.dto';

@ApiTags('customers-management')
@Controller('customers')
export class CustomersManagementController {
  private readonly logger = new Logger(CustomersManagementController.name);

  constructor(private readonly customersManagementService: CustomersManagementService) {}

  /**
   * Convert CustomerWithRelations to CustomerResponseDto
   */
  private mapToResponseDto(customer: CustomerWithRelations): CustomerResponseDto {
    return {
      ...customer,
      externalId: customer.externalId || undefined,
    } as CustomerResponseDto;
  }

  @Post()
  @ApiOperation({ 
    summary: 'Create a new customer',
    description: 'Create a new customer record with the provided information. All required fields must be included in the request body.'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Customer created successfully',
    type: CustomerResponseDto,
    examples: {
      success: {
        summary: 'Successful customer creation',
        value: {
          id: 'cmca6q7w40000l9015jo4lc5o',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phone: '+1234567890',
          status: 'ACTIVE',
          type: 'INDIVIDUAL',
          isEmailVerified: false,
          isKycVerified: false,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation errors',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'array', items: { type: 'string' }, example: ['email must be a valid email', 'firstName should not be empty'] },
        error: { type: 'string', example: 'Bad Request' },
        statusCode: { type: 'number', example: 400 }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Customer with this email already exists',
  })
  async createCustomer(
    @Body() createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Creating customer`);

    // Convert DTO to Prisma input
    const customerData = {
      ...createCustomerDto,
      dateOfBirth: createCustomerDto.dateOfBirth ? new Date(createCustomerDto.dateOfBirth) : undefined,
    };

    const customer = await this.customersManagementService.create(customerData);
    return this.mapToResponseDto(customer);
  }

  @Put(':id')
  @ApiOperation({ 
    summary: 'Update customer by ID',
    description: 'Update an existing customer record with new information. Only provided fields will be updated.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer updated successfully',
    type: CustomerResponseDto,
    examples: {
      success: {
        summary: 'Successful customer update',
        value: {
          id: 'cmca6q7w40000l9015jo4lc5o',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phone: '+1234567890',
          status: 'ACTIVE',
          type: 'INDIVIDUAL',
          isEmailVerified: true,
          isKycVerified: false,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T14:45:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation errors',
  })
  async updateCustomer(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Updating customer ${id}`);

    // Convert DTO to Prisma input
    const customerData = {
      ...updateCustomerDto,
      dateOfBirth: updateCustomerDto.dateOfBirth ? new Date(updateCustomerDto.dateOfBirth) : undefined,
    };

    const customer = await this.customersManagementService.update(id, customerData);
    return this.mapToResponseDto(customer);
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete customer by ID (soft delete)',
    description: 'Perform a soft delete on the customer record. The customer will be marked as deleted but data will be retained for audit purposes.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer deleted successfully',
    schema: { 
      type: 'object', 
      properties: { 
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Customer deleted successfully' }
      } 
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions to delete customer',
  })
  async deleteCustomer(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`Deleting customer ${id}`);
    await this.customersManagementService.delete(id);
    return {
      success: true,
      message: 'Customer deleted successfully'
    };
  }
}
