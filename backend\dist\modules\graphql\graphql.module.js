"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphQLConfigModule = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const apollo_1 = require("@nestjs/apollo");
const path_1 = require("path");
const config_service_1 = require("../../config/config.service");
const config_module_1 = require("../../config/config.module");
const prisma_module_1 = require("../../prisma/prisma.module");
const auth_shared_module_1 = require("../auth-shared/auth-shared.module");
const customers_query_module_1 = require("../customers-query/customers-query.module");
const customers_management_module_1 = require("../customers-management/customers-management.module");
const customers_verification_module_1 = require("../customers-verification/customers-verification.module");
const customers_admin_module_1 = require("../customers-admin/customers-admin.module");
const customer_resolver_1 = require("./resolvers/customer.resolver");
const auth_resolver_1 = require("./resolvers/auth.resolver");
const graphql_auth_guard_1 = require("./guards/graphql-auth.guard");
let GraphQLConfigModule = class GraphQLConfigModule {
};
exports.GraphQLConfigModule = GraphQLConfigModule;
exports.GraphQLConfigModule = GraphQLConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            graphql_1.GraphQLModule.forRootAsync({
                driver: apollo_1.ApolloDriver,
                imports: [config_module_1.ConfigModule],
                inject: [config_service_1.ConfigService],
                useFactory: async (configService) => ({
                    autoSchemaFile: (0, path_1.join)(process.cwd(), 'src/schema.gql'),
                    sortSchema: true,
                    playground: configService.get('NODE_ENV', 'development') === 'development' ? {
                        settings: {
                            'request.credentials': 'include',
                        },
                    } : false,
                    introspection: true,
                    context: ({ req, reply }) => ({ req, reply }),
                    formatError: (error) => {
                        console.error('GraphQL Error:', error);
                        return {
                            message: error.message,
                            code: error.extensions?.code,
                            path: error.path,
                        };
                    },
                    cors: {
                        origin: configService.getAllowedOrigins(),
                        credentials: true,
                    },
                }),
            }),
            config_module_1.ConfigModule,
            prisma_module_1.PrismaModule,
            auth_shared_module_1.AuthSharedModule,
            customers_query_module_1.CustomersQueryModule,
            customers_management_module_1.CustomersManagementModule,
            customers_verification_module_1.CustomersVerificationModule,
            customers_admin_module_1.CustomersAdminModule,
        ],
        providers: [customer_resolver_1.CustomerResolver, auth_resolver_1.AuthResolver, graphql_auth_guard_1.GraphQLAuthGuard],
        exports: [graphql_auth_guard_1.GraphQLAuthGuard],
    })
], GraphQLConfigModule);
//# sourceMappingURL=graphql.module.js.map