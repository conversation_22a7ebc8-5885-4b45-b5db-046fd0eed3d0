import { User, AuthConfig } from '../types/auth.types';

export class AuthService {
  private config: AuthConfig;

  constructor(config: AuthConfig) {
    this.config = config;
  }

  /**
   * Check if user has valid authentication cookies
   */
  hasAuthCookies(): boolean {
    if (typeof document === 'undefined') return false;

    console.log('🍪 Checking cookies - Raw document.cookie:', document.cookie);
    console.log('🍪 Current domain:', window.location.hostname);
    console.log('🍪 Current origin:', window.location.origin);

    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, string>);

    console.log('🍪 Parsed cookies:', cookies);
    console.log('🍪 Looking for cookies:', {
      accessToken: this.config.cookieNames.accessToken,
      refreshToken: this.config.cookieNames.refreshToken,
    });

    const hasAccessToken = !!cookies[this.config.cookieNames.accessToken];
    const hasRefreshToken = !!cookies[this.config.cookieNames.refreshToken];

    console.log('🍪 Cookie status:', {
      hasAccessToken,
      hasRefreshToken,
      accessTokenValue: hasAccessToken ? '***PRESENT***' : 'MISSING',
      refreshTokenValue: hasRefreshToken ? '***PRESENT***' : 'MISSING',
    });

    console.log('🍪 Local cookie check result:', hasAccessToken && hasRefreshToken);
    return hasAccessToken && hasRefreshToken;
  }

  /**
   * Alias for hasAuthCookies for backward compatibility
   */
  hasValidCookies(): boolean {
    return this.hasAuthCookies();
  }

  /**
   * Redirect to external auth service for login
   */
  login(redirectUrl?: string): void {
    const currentUrl = redirectUrl || window.location.href;
    const callbackUrl = `${window.location.origin}/auth/callback`;
    const loginUrl = `${this.config.authFrontendUrl}/login?redirect=${encodeURIComponent(callbackUrl)}&return_to=${encodeURIComponent(currentUrl)}`;

    console.log('🔐 Redirecting to login:', {
      currentUrl,
      callbackUrl,
      loginUrl
    });

    window.location.href = loginUrl;
  }

  /**
   * Logout by calling backend logout endpoint and redirecting to auth service
   */
  async logout(): Promise<void> {
    try {
      console.log('🔐 Starting logout process...');

      // First, call backend logout endpoint to clear server-side cookies
      await this.logoutFromBackend();

      // Then clear local cookies as fallback
      this.clearLocalCookies();

      // Finally, redirect to auth service logout
      console.log('🔄 Redirecting to auth service logout...');
      const logoutUrl = `${this.config.authFrontendUrl}/logout`;
      window.location.href = logoutUrl;
    } catch (error) {
      console.error('❌ Logout error:', error);

      // Even if backend logout fails, still clear local cookies and redirect
      this.clearLocalCookies();
      const logoutUrl = `${this.config.authFrontendUrl}/logout`;
      window.location.href = logoutUrl;
    }
  }

  /**
   * Call backend logout endpoint to clear server-side cookies
   */
  private async logoutFromBackend(): Promise<void> {
    try {
      console.log('🔐 Calling backend logout endpoint...');

      const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
      if (!graphqlUrl) {
        throw new Error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }

      // Try GraphQL logout mutation first
      try {
        const { apolloClient } = await import('@/lib/apollo-client');
        const { gql } = await import('@apollo/client');

        const LOGOUT_MUTATION = gql`
          mutation Logout {
            logout
          }
        `;

        const { data } = await apolloClient.mutate({
          mutation: LOGOUT_MUTATION,
          errorPolicy: 'all',
        });

        if (data?.logout) {
          console.log('✅ Backend logout successful via GraphQL');
          return;
        }
      } catch (apolloError) {
        console.log('⚠️ GraphQL logout failed, trying REST API...');
      }

      // Fallback to REST API logout
      const apiUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL;
      if (apiUrl) {
        const response = await fetch(`${apiUrl}/auth/logout`, {
          method: 'POST',
          credentials: 'include', // Include cookies
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          console.log('✅ Backend logout successful via REST API');
          return;
        }
      }

      throw new Error('Both GraphQL and REST logout failed');
    } catch (error) {
      console.error('❌ Backend logout failed:', error);
      throw error;
    }
  }

  /**
   * Clear local cookies as fallback
   */
  private clearLocalCookies(): void {
    console.log('🧹 Clearing local cookies...');

    // Clear cookies with different domain configurations
    const domains = [
      '', // Current domain
      window.location.hostname,
      `.${window.location.hostname}`,
      this.config.domains?.cookieDomain || '', // Configured cookie domain
    ];

    domains.forEach(domain => {
      const cookieOptions = domain ? `; domain=${domain}` : '';
      document.cookie = `${this.config.cookieNames.accessToken}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/${cookieOptions}`;
      document.cookie = `${this.config.cookieNames.refreshToken}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/${cookieOptions}`;
    });

    console.log('✅ Local cookies cleared');
  }

  /**
   * Get user info from backend using GraphQL (backend will decrypt and verify tokens)
   * NOTE: This method requires Apollo Client to be configured in your project
   * Import path may need to be adjusted based on your project structure
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      console.log('🔍 Getting current user via GraphQL...');

      const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
      if (!graphqlUrl) {
        throw new Error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }

      console.log('🌐 GraphQL URL:', graphqlUrl);

      // Import Apollo client dynamically to avoid SSR issues
      // NOTE: Adjust this import path based on your project structure
      try {
        const { apolloClient } = await import('@/lib/apollo-client');
        const { gql } = await import('@apollo/client');

        const ME_COOKIES_QUERY = gql`
          query MeCookies {
            meCookies {
              id
              email
              username
              firstName
              lastName
              role
              permissions
              createdAt
              updatedAt
            }
          }
        `;

        const { data } = await apolloClient.query({
          query: ME_COOKIES_QUERY,
          fetchPolicy: 'network-only',
          errorPolicy: 'all',
        });

        if (data?.meCookies) {
          console.log('✅ User authenticated via GraphQL:', data.meCookies.email);
          return data.meCookies;
        }

        console.log('❌ No user data returned from GraphQL');
        return null;
      } catch (importError) {
        console.error('❌ Failed to import Apollo client. Make sure @/lib/apollo-client is configured in your project:', importError);

        // Fallback: Try direct fetch to GraphQL endpoint
        return this.getCurrentUserViaFetch();
      }
    } catch (error) {
      console.error('❌ Error getting current user via GraphQL:', error);
      return null;
    }
  }

  /**
   * Fallback method to get user via direct fetch (when Apollo Client is not available)
   */
  private async getCurrentUserViaFetch(): Promise<User | null> {
    try {
      console.log('🔄 Falling back to direct fetch for user authentication...');

      const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
      if (!graphqlUrl) {
        throw new Error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }

      const response = await fetch(graphqlUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies
        body: JSON.stringify({
          query: `
            query MeCookies {
              meCookies {
                id
                email
                username
                firstName
                lastName
                role
                permissions
                createdAt
                updatedAt
              }
            }
          `
        })
      });

      const result = await response.json();

      if (result.data?.meCookies) {
        console.log('✅ User authenticated via fetch:', result.data.meCookies.email);
        return result.data.meCookies;
      }

      console.log('❌ No user data returned from fetch');
      return null;
    } catch (error) {
      console.error('❌ Error getting current user via fetch:', error);
      return null;
    }
  }

  /**
   * Refresh authentication using GraphQL
   * NOTE: This method requires Apollo Client to be configured in your project
   */
  async refreshAuth(): Promise<boolean> {
    try {
      console.log('🔄 Refreshing authentication via GraphQL...');

      const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
      if (!graphqlUrl) {
        throw new Error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }

      // Import Apollo client dynamically to avoid SSR issues
      // NOTE: Adjust this import path based on your project structure
      try {
        const { apolloClient } = await import('@/lib/apollo-client');
        const { gql } = await import('@apollo/client');

        const REFRESH_AUTH_MUTATION = gql`
          mutation RefreshAuth {
            refreshAuth
          }
        `;

        const { data } = await apolloClient.mutate({
          mutation: REFRESH_AUTH_MUTATION,
          errorPolicy: 'all',
        });

        if (data?.refreshAuth) {
          console.log('✅ Authentication refreshed successfully via GraphQL');
          return true;
        }

        console.log('❌ Failed to refresh authentication via GraphQL');
        return false;
      } catch (importError) {
        console.error('❌ Failed to import Apollo client for refresh. Make sure @/lib/apollo-client is configured:', importError);
        // For refresh, we can just return true since the main getCurrentUser will handle the actual auth check
        return true;
      }
    } catch (error) {
      console.error('❌ Error refreshing authentication via GraphQL:', error);
      return false;
    }
  }
}

// Configuration - with fallbacks for build time
export const defaultAuthConfig: AuthConfig = {
  authFrontendUrl: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL || 'https://ng-auth-fe-dev.dev1.ngnair.com',
  authJwksUrl: process.env.NEXT_PUBLIC_AUTH_JWKS_URL || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
  encryptionKey: process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY || 'fallback-key-for-build',
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },
  domains: {
    cookieDomain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
    allowedOrigins: process.env.NEXT_PUBLIC_ALLOWED_ORIGINS?.split(','),
  },
};

// Validate required environment variables (only in browser, but don't throw errors)
if (typeof window !== 'undefined') {
  if (!process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL) {
    console.error('❌ NEXT_PUBLIC_AUTH_FRONTEND_URL is missing');
  }
  if (!process.env.NEXT_PUBLIC_AUTH_JWKS_URL) {
    console.error('❌ NEXT_PUBLIC_AUTH_JWKS_URL is missing');
  }
  if (!process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY) {
    console.error('❌ NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY is missing');
  }
  if (process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL && process.env.NEXT_PUBLIC_AUTH_JWKS_URL && process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY) {
    console.log('✅ All auth environment variables validated successfully');
  }
}

// Export singleton instance
export const authService = new AuthService(defaultAuthConfig);
