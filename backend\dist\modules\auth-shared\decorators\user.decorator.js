"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetCurrentUser = exports.CurrentUser = exports.getCurrentUser = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
exports.getCurrentUser = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
});
exports.CurrentUser = exports.getCurrentUser;
exports.GetCurrentUser = (0, common_1.createParamDecorator)((data, context) => {
    const gqlContext = graphql_1.GqlExecutionContext.create(context);
    if (gqlContext.getContext().req) {
        return gqlContext.getContext().req.user;
    }
    const request = context.switchToHttp().getRequest();
    return request.user;
});
//# sourceMappingURL=user.decorator.js.map