import { useEffect } from 'react';
import '../styles/globals.css';
import type { AppProps } from 'next/app';
import { ApolloProvider } from '@apollo/client';
import { apolloClient } from '@/lib/apollo-client';
import { Toaster } from 'sonner';
import { AuthProvider } from '../auth';

function MyApp({ Component, pageProps }: AppProps) {
  // Add debugging for customer microservice
  useEffect(() => {
    console.log('Customer Management System initialized');
    console.log('Environment variables:');
    if (typeof window !== 'undefined') {
      console.log('Running in browser');
      console.log('Customer GraphQL URL:', process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL);

      // Validate required environment variables
      if (!process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL) {
        console.error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }
    }
  }, []);

  return (
    <AuthProvider>
      <ApolloProvider client={apolloClient}>
        <Component {...pageProps} />
        <Toaster position="top-right" />
      </ApolloProvider>
    </AuthProvider>
  );
}

export default MyApp;
