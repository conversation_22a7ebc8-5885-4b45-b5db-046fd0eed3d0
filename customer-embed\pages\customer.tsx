import React, { useEffect, useState } from 'react';
import { GetServerSideProps } from 'next';
import Layout from '../src/components/Layout';
import CustomerForm from '@/components/CustomerForm';
import { EmbedConfig, CustomerResponse, TaxCalculationResponse } from '@/types/customer';

export default function Customer() {
  const [config, setConfig] = useState<EmbedConfig>({});
  const [purchaseAmount, setPurchaseAmount] = useState(0);

  useEffect(() => {
    // Listen for configuration from parent window (if embedded in iframe)
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'CUSTOMER_EMBED_CONFIG') {
        setConfig(event.data.config || {});
        setPurchaseAmount(event.data.purchaseAmount || 0);
      }
    };

    window.addEventListener('message', handleMessage);
    
    // Send ready message to parent
    if (window.parent !== window) {
      window.parent.postMessage({ type: 'CUSTOMER_EMBED_READY' }, '*');
    }

    // Check for URL parameters for standalone usage
    const urlParams = new URLSearchParams(window.location.search);
    const amount = urlParams.get('amount');
    if (amount) {
      setPurchaseAmount(parseFloat(amount));
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handleCustomerCreated = (customer: CustomerResponse) => {
    // Send customer data to parent window
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'CUSTOMER_CREATED',
        customer,
      }, '*');
    }
    
    config.onCustomerCreated?.(customer);
  };

  const handleTaxCalculated = (tax: TaxCalculationResponse) => {
    // Send tax data to parent window
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'TAX_CALCULATED',
        tax,
      }, '*');
    }
    
    config.onTaxCalculated?.(tax);
  };

  const handleError = (error: string) => {
    // Send error to parent window
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'CUSTOMER_ERROR',
        error,
      }, '*');
    }
    
    config.onError?.(error);
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Customer Information
              </h1>
              <p className="text-gray-600">
                Enter customer details for payment processing
              </p>
            </div>
            
            <CustomerForm
              config={{
                ...config,
                onCustomerCreated: handleCustomerCreated,
                onTaxCalculated: handleTaxCalculated,
                onError: handleError,
              }}
              purchaseAmount={purchaseAmount}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}

// Disable static generation for this page since it uses auth context
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {},
  };
};
