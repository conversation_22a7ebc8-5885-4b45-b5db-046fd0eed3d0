export interface Config {
    readonly NODE_ENV?: string;
    readonly API_PORT: number;
    readonly API_PREFIX: string;
    readonly SWAGGER_ENABLE: number;
    readonly JWT_SECRET: string;
    readonly JWT_ISSUER: string;
    readonly JWT_EXPIRATION?: string;
    readonly B2_KEY_ID: string;
    readonly B2_APP_KEY: string;
    readonly B2_BUCKET_ID: string;
    readonly B2_ENDPOINT: string;
    readonly PARTNER_WEBHOOK_URL?: string;
    readonly PARTNER_EMAIL_CONFIG?: {
        apiUrl: string;
        recipients?: string[];
        teamDistributionList?: string;
        escalationDistributionList?: string;
        defaultFrom?: string;
        replyTo?: string;
    };
    readonly SOFTWARE_PARTNER_WEBHOOK_URL?: string;
    readonly ALLOWED_ORIGINS?: string;
    readonly ALLOWED_ORIGIN_1?: string;
    readonly ALLOWED_ORIGIN_2?: string;
    readonly ALLOWED_ORIGIN_3?: string;
    readonly ALLOWED_ORIGIN_4?: string;
    readonly ALLOWED_ORIGIN_5?: string;
    readonly ALLOWED_ORIGIN_6?: string;
    readonly CORS_BLOCK_LOCALHOST?: boolean;
    readonly PORT: number;
    readonly AUTH_SERVICE_URL: string;
    readonly AUTH_FRONTEND_URL?: string;
    readonly AUTH_JWKS_URL?: string;
    readonly ACCESS_TOKEN_ENCRYPTION_KEY?: string;
    readonly LOCAL_BACKEND_DOMAIN?: string;
    readonly LOCAL_FRONTEND_DOMAIN?: string;
    readonly LOCAL_ADMIN_DOMAIN?: string;
}
