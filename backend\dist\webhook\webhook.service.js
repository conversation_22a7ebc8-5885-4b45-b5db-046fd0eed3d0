"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WebhookService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let WebhookService = WebhookService_1 = class WebhookService {
    prisma;
    logger = new common_1.Logger(WebhookService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async processWebhook(payload) {
        try {
            this.logger.log(`Processing webhook: ${JSON.stringify(payload)}`);
            const webhookType = this.determineWebhookType(payload);
            switch (webhookType) {
                case 'elavon':
                    return await this.processElavonWebhook(payload);
                case 'tsep':
                    return await this.processTSEPWebhook(payload);
                default:
                    this.logger.warn(`Unknown webhook type for payload: ${JSON.stringify(payload)}`);
                    return {
                        status: "success",
                        message: "Webhook received but not processed (unknown type)",
                        timestamp: new Date().toISOString()
                    };
            }
        }
        catch (error) {
            if (error instanceof Error) {
                this.logger.error(`Error processing webhook: ${error.message}`, error.stack);
            }
            else {
                this.logger.error("Unknown error processing webhook");
            }
            throw error;
        }
    }
    determineWebhookType(payload) {
        if (payload.ssl_token && payload.raw_response &&
            payload.raw_response?.ssl_token) {
            return 'elavon';
        }
        if (payload.tsep_token && payload.raw_response &&
            payload.raw_response?.tsepToken) {
            return 'tsep';
        }
        return 'unknown';
    }
    async processElavonWebhook(payload) {
        this.logger.log(`Processing Elavon webhook for customer: ${payload.customer_id}`);
        try {
            const customer = await this.findOrCreateCustomer({
                customerId: payload.customer_id,
                customerName: payload.customer_name,
                email: payload.raw_response.ssl_email,
                firstName: payload.raw_response.ssl_first_name,
                lastName: payload.raw_response.ssl_last_name,
                country: payload.raw_response.ssl_country
            });
            const paymentToken = await this.prisma.paymentToken.create({
                data: {
                    customerId: customer.id,
                    tokenHash: this.hashToken(payload.ssl_token),
                    externalTokenId: payload.ssl_token,
                    paymentProvider: client_1.PaymentProvider.ELAVON,
                    tokenType: client_1.PaymentTokenType.CARD,
                    status: payload.status === '0' ? client_1.PaymentTokenStatus.ACTIVE : client_1.PaymentTokenStatus.SUSPENDED,
                    maskedInfo: payload.raw_response.ssl_card_number,
                    paymentBrand: this.mapCardType(payload.card_type),
                    expiresAt: this.parseElavonExpiryDate(payload.raw_response.ssl_exp_date),
                    elavonToken: payload.ssl_token,
                    elavonTransactionId: payload.raw_response.ssl_txn_id,
                    elavonReferenceId: payload.reference_id,
                    elavonCardType: payload.card_type,
                    elavonApprovalCode: payload.raw_response.ssl_approval_code,
                    transactionAmount: payload.amount,
                    currency: payload.currency,
                    customerName: payload.customer_name,
                    customerEmail: payload.raw_response.ssl_email,
                    rawWebhookData: payload,
                    providerMetadata: payload.raw_response,
                    financeId: null,
                    accountId: null,
                }
            });
            this.logger.log(`Created Elavon payment token: ${paymentToken.id}`);
            return {
                status: "success",
                message: "Elavon webhook processed successfully",
                timestamp: new Date().toISOString(),
                paymentTokenId: paymentToken.id,
                customerId: customer.id
            };
        }
        catch (error) {
            this.logger.error(`Error processing Elavon webhook: ${error.message}`, error.stack);
            throw error;
        }
    }
    async processTSEPWebhook(payload) {
        this.logger.log(`Processing TSEP webhook for customer: ${payload.customer_id}`);
        try {
            const customer = await this.findOrCreateCustomer({
                customerId: payload.customer_id,
                customerName: payload.customer_name,
                email: undefined,
                firstName: payload.raw_response.cardHolderName?.split(' ')[0],
                lastName: payload.raw_response.cardHolderName?.split(' ').slice(1).join(' '),
                country: 'US'
            });
            const paymentToken = await this.prisma.paymentToken.create({
                data: {
                    customerId: customer.id,
                    tokenHash: this.hashToken(payload.tsep_token),
                    externalTokenId: payload.tsep_token,
                    paymentProvider: client_1.PaymentProvider.TSEP,
                    tokenType: client_1.PaymentTokenType.CARD,
                    status: payload.status === 'PASS' ? client_1.PaymentTokenStatus.ACTIVE : client_1.PaymentTokenStatus.SUSPENDED,
                    maskedInfo: `**** **** **** ${payload.raw_response.maskedCardNumber}`,
                    paymentBrand: this.mapCardType(payload.card_type),
                    expiresAt: this.parseTSEPExpiryDate(payload.raw_response.expirationDate),
                    tsepToken: payload.tsep_token,
                    tsepTransactionId: payload.raw_response.transactionID,
                    tsepTransactionKey: payload.transaction_key,
                    tsepDeviceId: payload.device_id,
                    tsepCardType: payload.card_type,
                    tsepResponseCode: payload.response_code,
                    transactionAmount: payload.amount,
                    currency: payload.currency,
                    customerName: payload.customer_name,
                    customerEmail: null,
                    rawWebhookData: payload,
                    providerMetadata: payload.raw_response,
                    financeId: null,
                    accountId: null,
                }
            });
            this.logger.log(`Created TSEP payment token: ${paymentToken.id}`);
            return {
                status: "success",
                message: "TSEP webhook processed successfully",
                timestamp: new Date().toISOString(),
                paymentTokenId: paymentToken.id,
                customerId: customer.id
            };
        }
        catch (error) {
            this.logger.error(`Error processing TSEP webhook: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findOrCreateCustomer(customerData) {
        let customer = await this.prisma.customer.findFirst({
            where: {
                externalId: customerData.customerId
            }
        });
        if (!customer && customerData.email) {
            customer = await this.prisma.customer.findFirst({
                where: {
                    email: customerData.email
                }
            });
        }
        if (!customer) {
            const [firstName, ...lastNameParts] = customerData.customerName.split(' ');
            const lastName = lastNameParts.join(' ') || '';
            customer = await this.prisma.customer.create({
                data: {
                    externalId: customerData.customerId,
                    firstName: customerData.firstName || firstName,
                    lastName: customerData.lastName || lastName,
                    email: customerData.email || `${customerData.customerId}@placeholder.com`,
                    status: 'ACTIVE',
                    type: 'INDIVIDUAL',
                }
            });
            this.logger.log(`Created new customer: ${customer.id} for external ID: ${customerData.customerId}`);
        }
        else {
            this.logger.log(`Found existing customer: ${customer.id} for external ID: ${customerData.customerId}`);
        }
        return customer;
    }
    hashToken(token) {
        const crypto = require('crypto');
        return crypto.createHash('sha256').update(token).digest('hex');
    }
    mapCardType(cardType) {
        const cardTypeMap = {
            'DISC': 'Discover',
            'VISA': 'Visa',
            'MC': 'Mastercard',
            'AMEX': 'American Express',
            'V': 'Visa',
            'M': 'Mastercard',
            'A': 'American Express',
            'D': 'Discover'
        };
        return cardTypeMap[cardType] || cardType;
    }
    parseElavonExpiryDate(expiryDate) {
        if (!expiryDate || expiryDate.length !== 4) {
            return null;
        }
        const month = parseInt(expiryDate.substring(0, 2), 10);
        const year = parseInt('20' + expiryDate.substring(2, 4), 10);
        if (month < 1 || month > 12) {
            return null;
        }
        return new Date(year, month - 1, new Date(year, month, 0).getDate());
    }
    parseTSEPExpiryDate(expiryDate) {
        if (!expiryDate) {
            return null;
        }
        const parts = expiryDate.split('/');
        if (parts.length !== 2) {
            return null;
        }
        const month = parseInt(parts[0], 10);
        const year = parseInt(parts[1], 10);
        if (month < 1 || month > 12 || year < 2000) {
            return null;
        }
        return new Date(year, month - 1, new Date(year, month, 0).getDate());
    }
};
exports.WebhookService = WebhookService;
exports.WebhookService = WebhookService = WebhookService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WebhookService);
//# sourceMappingURL=webhook.service.js.map