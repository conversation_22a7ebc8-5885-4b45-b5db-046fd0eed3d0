import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { ProtectedRoute } from '../auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CustomerGraphQLService } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { 
  Users, 
  UserCheck, 
  UserX, 
  Activity,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface DashboardStats {
  totalCustomers: number;
  verifiedCustomers: number;
  pendingVerification: number;
  recentActivity: number;
}

function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalCustomers: 0,
    verifiedCustomers: 0,
    pendingVerification: 0,
    recentActivity: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setConnectionStatus('checking');
      
      // Test GraphQL connection by fetching customers
      const customersData = await CustomerGraphQLService.getCustomers(1, 100);
      
      // Calculate stats from the data
      const totalCustomers = customersData.total;
      const verifiedCustomers = customersData.data.filter(
        customer => customer.emailVerified && customer.phoneVerified
      ).length;
      const pendingVerification = customersData.data.filter(
        customer => !customer.emailVerified || !customer.phoneVerified
      ).length;

      setStats({
        totalCustomers,
        verifiedCustomers,
        pendingVerification,
        recentActivity: Math.floor(totalCustomers * 0.1), // Mock recent activity
      });

      setConnectionStatus('connected');
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      setConnectionStatus('error');
      toast.error('Failed to connect to customer service');
    } finally {
      setIsLoading(false);
    }
  };

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    trend,
    color = 'default'
  }: {
    title: string;
    value: number | string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
    trend?: string;
    color?: 'default' | 'success' | 'warning' | 'error';
  }) => {
    const colorClasses = {
      default: 'text-primary',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      error: 'text-red-600',
    };

    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <Icon className={`h-4 w-4 ${colorClasses[color]}`} />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{isLoading ? '...' : value}</div>
          <p className="text-xs text-muted-foreground">{description}</p>
          {trend && (
            <p className="text-xs text-green-600 mt-1">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              {trend}
            </p>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <AdminLayout title="Dashboard">
      <div className="space-y-6">
        {/* Connection Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'error' ? 'bg-red-500' :
                'bg-yellow-500'
              }`} />
              <span>Customer Service Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {connectionStatus === 'connected' && 'Successfully connected to customer microservice'}
              {connectionStatus === 'error' && 'Failed to connect to customer microservice'}
              {connectionStatus === 'checking' && 'Checking connection to customer microservice...'}
            </p>
            {connectionStatus === 'error' && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={loadDashboardData}
                className="mt-2"
              >
                Retry Connection
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Customers"
            value={stats.totalCustomers}
            description="All registered customers"
            icon={Users}
            trend="+12% from last month"
          />
          <StatCard
            title="Verified Customers"
            value={stats.verifiedCustomers}
            description="Fully verified accounts"
            icon={CheckCircle}
            color="success"
            trend="+8% from last month"
          />
          <StatCard
            title="Pending Verification"
            value={stats.pendingVerification}
            description="Awaiting verification"
            icon={Clock}
            color="warning"
          />
          <StatCard
            title="Recent Activity"
            value={stats.recentActivity}
            description="Actions in last 24h"
            icon={Activity}
            trend="+5% from yesterday"
          />
        </div>

        {/* Quick Actions */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customer Management</CardTitle>
              <CardDescription>
                View and manage customer accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" onClick={() => window.location.href = '/customers'}>
                <Users className="mr-2 h-4 w-4" />
                View All Customers
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Verification Queue</CardTitle>
              <CardDescription>
                Process pending verifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" onClick={() => window.location.href = '/verification'}>
                <UserCheck className="mr-2 h-4 w-4" />
                Review Verifications
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Activity Log</CardTitle>
              <CardDescription>
                Monitor system activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" onClick={() => window.location.href = '/activity'}>
                <Activity className="mr-2 h-4 w-4" />
                View Activity Log
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest customer management activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {isLoading ? (
                <p className="text-sm text-muted-foreground">Loading recent activity...</p>
              ) : (
                <>
                  <div className="flex items-center space-x-3 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Customer email verification completed</span>
                    <span className="text-muted-foreground">2 minutes ago</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span>New customer registration</span>
                    <span className="text-muted-foreground">15 minutes ago</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span>KYC verification pending review</span>
                    <span className="text-muted-foreground">1 hour ago</span>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

function ProtectedAdminDashboard() {
  return (
    <ProtectedRoute requiredRole="admin">
      <AdminDashboard />
    </ProtectedRoute>
  );
}

export default ProtectedAdminDashboard;
