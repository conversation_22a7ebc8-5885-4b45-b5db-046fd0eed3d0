# Customer Embed Frontend Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm install

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build arguments for environment variables (optional for build)
ARG NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY
ARG NEXT_PUBLIC_AUTH_FRONTEND_URL
ARG NEXT_PUBLIC_AUTH_JWKS_URL
ARG NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL
ARG NEXT_PUBLIC_COOKIE_DOMAIN
ARG NEXT_PUBLIC_ALLOWED_ORIGINS

# Set environment variables for build (with fallbacks)
ENV NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=$NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY
ENV NEXT_PUBLIC_AUTH_FRONTEND_URL=$NEXT_PUBLIC_AUTH_FRONTEND_URL
ENV NEXT_PUBLIC_AUTH_JWKS_URL=$NEXT_PUBLIC_AUTH_JWKS_URL
ENV NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=$NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL
ENV NEXT_PUBLIC_COOKIE_DOMAIN=$NEXT_PUBLIC_COOKIE_DOMAIN
ENV NEXT_PUBLIC_ALLOWED_ORIGINS=$NEXT_PUBLIC_ALLOWED_ORIGINS

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3061

ENV PORT=3061
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
