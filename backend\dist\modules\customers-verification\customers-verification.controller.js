"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CustomersVerificationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersVerificationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const customers_verification_service_1 = require("./customers-verification.service");
const customer_shared_dto_1 = require("../customers-shared/dto/customer-shared.dto");
let CustomersVerificationController = CustomersVerificationController_1 = class CustomersVerificationController {
    customersVerificationService;
    logger = new common_1.Logger(CustomersVerificationController_1.name);
    constructor(customersVerificationService) {
        this.customersVerificationService = customersVerificationService;
    }
    mapToResponseDto(customer) {
        return {
            ...customer,
            externalId: customer.externalId || undefined,
        };
    }
    async verifyEmail(id) {
        this.logger.log(`Verifying email for customer ${id}`);
        const customer = await this.customersVerificationService.verifyEmail(id);
        return this.mapToResponseDto(customer);
    }
    async verifyPhone(id) {
        this.logger.log(`Verifying phone for customer ${id}`);
        const customer = await this.customersVerificationService.verifyPhone(id);
        return this.mapToResponseDto(customer);
    }
    async verifyKyc(id) {
        this.logger.log(`Verifying KYC for customer ${id}`);
        const customer = await this.customersVerificationService.verifyKyc(id);
        return this.mapToResponseDto(customer);
    }
};
exports.CustomersVerificationController = CustomersVerificationController;
__decorate([
    (0, common_1.Post)(':id/verify-email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Verify customer email address',
        description: 'Mark a customer\'s email address as verified. This is typically called after the customer has completed email verification process.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Email verified successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
        examples: {
            success: {
                summary: 'Successful email verification',
                value: {
                    id: 'cmca6q7w40000l9015jo4lc5o',
                    email: '<EMAIL>',
                    firstName: 'John',
                    lastName: 'Doe',
                    isEmailVerified: true,
                    isKycVerified: false,
                    status: 'ACTIVE',
                    verifiedAt: '2024-01-15T14:30:00Z',
                    updatedAt: '2024-01-15T14:30:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Email already verified or customer not eligible for verification',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersVerificationController.prototype, "verifyEmail", null);
__decorate([
    (0, common_1.Post)(':id/verify-phone'),
    (0, swagger_1.ApiOperation)({
        summary: 'Verify customer phone number',
        description: 'Mark a customer\'s phone number as verified. This is typically called after the customer has completed phone verification process (e.g., SMS OTP).'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Phone verified successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
        examples: {
            success: {
                summary: 'Successful phone verification',
                value: {
                    id: 'cmca6q7w40000l9015jo4lc5o',
                    email: '<EMAIL>',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '+1234567890',
                    isEmailVerified: true,
                    isPhoneVerified: true,
                    isKycVerified: false,
                    status: 'ACTIVE',
                    verifiedAt: '2024-01-15T14:30:00Z',
                    updatedAt: '2024-01-15T14:35:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Phone already verified or customer not eligible for verification',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersVerificationController.prototype, "verifyPhone", null);
__decorate([
    (0, common_1.Post)(':id/verify-kyc'),
    (0, swagger_1.ApiOperation)({
        summary: 'Verify customer KYC (Know Your Customer)',
        description: 'Mark a customer as KYC verified after completing identity verification process. This typically involves document verification and identity checks.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Customer unique identifier (UUID)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'KYC verified successfully',
        type: customer_shared_dto_1.CustomerResponseDto,
        examples: {
            success: {
                summary: 'Successful KYC verification',
                value: {
                    id: 'cmca6q7w40000l9015jo4lc5o',
                    email: '<EMAIL>',
                    firstName: 'John',
                    lastName: 'Doe',
                    isEmailVerified: true,
                    isPhoneVerified: true,
                    isKycVerified: true,
                    status: 'ACTIVE',
                    kycVerifiedAt: '2024-01-15T15:00:00Z',
                    updatedAt: '2024-01-15T15:00:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'KYC already verified or customer not eligible for KYC verification',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to verify KYC',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomersVerificationController.prototype, "verifyKyc", null);
exports.CustomersVerificationController = CustomersVerificationController = CustomersVerificationController_1 = __decorate([
    (0, swagger_1.ApiTags)('customers-verification'),
    (0, common_1.Controller)('customers'),
    __metadata("design:paramtypes", [customers_verification_service_1.CustomersVerificationService])
], CustomersVerificationController);
//# sourceMappingURL=customers-verification.controller.js.map