import { ObjectType, Field, InputType } from '@nestjs/graphql';

// Export all types for GraphQL schema generation

@ObjectType()
export class User {
  @Field()
  id: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  username?: string;

  @Field({ nullable: true })
  firstName?: string;

  @Field({ nullable: true })
  lastName?: string;

  @Field({ nullable: true })
  role?: string;

  @Field(() => [String], { nullable: true })
  permissions?: string[];

  @Field()
  createdAt: string;

  @Field()
  updatedAt: string;
}

@ObjectType()
export class AuthStatus {
  @Field()
  status: string;

  @Field()
  service: string;

  @Field()
  timestamp: string;

  @Field()
  version: string;
}

@InputType()
export class DecryptTokenInput {
  @Field()
  token: string;
}

@ObjectType()
export class DecryptTokenResponse {
  @Field()
  decryptedToken: string;

  @Field()
  tokenType: string;

  @Field()
  success: boolean;

  @Field({ nullable: true })
  error?: string;
}

@InputType()
export class DecodeJwtInput {
  @Field()
  jwt: string;
}

@ObjectType()
export class DecodeJwtResponse {
  @Field()
  header: string;

  @Field()
  payload: string;

  @Field()
  signature: string;

  @Field()
  success: boolean;

  @Field({ nullable: true })
  error?: string;
}
