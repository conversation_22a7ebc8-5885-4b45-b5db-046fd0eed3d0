const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Clear existing data (optional - comment out if you want to keep existing data)
  console.log('🧹 Cleaning existing data...');
  await prisma.auditLog.deleteMany();
  await prisma.customerPreference.deleteMany();
  await prisma.contact.deleteMany();
  await prisma.address.deleteMany();
  await prisma.customer.deleteMany();

  // Create sample customers with addresses
  console.log('👥 Creating sample customers...');

  const customers = [
    {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      dateOfBirth: new Date('1985-03-15'),
      status: 'ACTIVE',
      type: 'INDIVIDUAL',
      isEmailVerified: true,
      isPhoneVerified: true,
      isKycVerified: false,
      tags: ['premium', 'early-adopter'],
      notes: 'Premium customer with excellent payment history',
      addresses: {
        create: [
          {
            type: 'HOME',
            label: 'Home Address',
            street1: '123 Main Street',
            street2: 'Apt 4B',
            city: 'New York',
            state: 'NY',
            postalCode: '10001',
            country: 'US',
            isDefault: true,
            isVerified: true,
          },
          {
            type: 'BILLING',
            label: 'Billing Address',
            street1: '456 Business Ave',
            city: 'New York',
            state: 'NY',
            postalCode: '10002',
            country: 'US',
            isDefault: false,
            isVerified: true,
          }
        ]
      }
    },
    {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '******-0124',
      dateOfBirth: new Date('1990-07-22'),
      status: 'ACTIVE',
      type: 'INDIVIDUAL',
      isEmailVerified: true,
      isPhoneVerified: false,
      isKycVerified: true,
      tags: ['verified'],
      notes: 'Completed KYC verification',
      addresses: {
        create: [
          {
            type: 'HOME',
            label: 'Home',
            street1: '789 Oak Street',
            city: 'Los Angeles',
            state: 'CA',
            postalCode: '90210',
            country: 'US',
            isDefault: true,
            isVerified: false,
          }
        ]
      }
    },
    {
      firstName: 'Michael',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '******-0125',
      dateOfBirth: new Date('1978-11-08'),
      companyName: 'Johnson Enterprises LLC',
      taxId: '12-3456789',
      businessType: 'Technology Consulting',
      status: 'ACTIVE',
      type: 'BUSINESS',
      isEmailVerified: true,
      isPhoneVerified: true,
      isKycVerified: true,
      tags: ['business', 'enterprise', 'high-value'],
      notes: 'Large enterprise client with multiple locations',
      addresses: {
        create: [
          {
            type: 'WORK',
            label: 'Corporate Headquarters',
            street1: '1000 Corporate Blvd',
            street2: 'Suite 500',
            city: 'Chicago',
            state: 'IL',
            postalCode: '60601',
            country: 'US',
            isDefault: true,
            isVerified: true,
          },
          {
            type: 'BILLING',
            label: 'Billing Department',
            street1: '1000 Corporate Blvd',
            street2: 'Suite 200',
            city: 'Chicago',
            state: 'IL',
            postalCode: '60601',
            country: 'US',
            isDefault: false,
            isVerified: true,
          }
        ]
      }
    },
    {
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '******-0126',
      dateOfBirth: new Date('1992-05-14'),
      status: 'PENDING_VERIFICATION',
      type: 'INDIVIDUAL',
      isEmailVerified: false,
      isPhoneVerified: false,
      isKycVerified: false,
      tags: ['new-customer'],
      notes: 'Recently registered, pending email verification',
      addresses: {
        create: [
          {
            type: 'HOME',
            label: 'Home',
            street1: '321 Pine Street',
            city: 'Seattle',
            state: 'WA',
            postalCode: '98101',
            country: 'US',
            isDefault: true,
            isVerified: false,
          }
        ]
      }
    },
    {
      firstName: 'Robert',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '******-0127',
      dateOfBirth: new Date('1965-12-03'),
      status: 'SUSPENDED',
      type: 'INDIVIDUAL',
      isEmailVerified: true,
      isPhoneVerified: true,
      isKycVerified: false,
      tags: ['suspended', 'review-required'],
      notes: 'Account suspended pending compliance review',
      addresses: {
        create: [
          {
            type: 'HOME',
            label: 'Home',
            street1: '654 Elm Street',
            city: 'Miami',
            state: 'FL',
            postalCode: '33101',
            country: 'US',
            isDefault: true,
            isVerified: true,
          }
        ]
      }
    }
  ];

  for (const customerData of customers) {
    const customer = await prisma.customer.create({
      data: customerData,
      include: {
        addresses: true,
      },
    });
    console.log(`✅ Created customer: ${customer.firstName} ${customer.lastName} (${customer.email})`);
  }

  console.log('🎉 Database seeding completed successfully!');
  console.log(`📊 Created ${customers.length} customers with addresses`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
