"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = exports.AdminGuard = exports.ApiGuard = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("../auth.service");
let ApiGuard = class ApiGuard {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        try {
            const cookies = request.cookies || {};
            const user = await this.authService.authenticateFromCookies(cookies);
            request.user = user;
            return true;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Authentication required');
        }
    }
};
exports.ApiGuard = ApiGuard;
exports.ApiGuard = ApiGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [auth_service_1.AuthSharedService])
], ApiGuard);
let AdminGuard = class AdminGuard {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        try {
            const cookies = request.cookies || {};
            const user = await this.authService.authenticateFromCookies(cookies);
            if (!this.authService.hasRole(user, 'admin') && !this.authService.hasRole(user, 'global_admin')) {
                throw new common_1.ForbiddenException('Admin access required');
            }
            request.user = user;
            return true;
        }
        catch (error) {
            if (error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.UnauthorizedException('Authentication required');
        }
    }
};
exports.AdminGuard = AdminGuard;
exports.AdminGuard = AdminGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [auth_service_1.AuthSharedService])
], AdminGuard);
var auth_service_2 = require("../auth.service");
Object.defineProperty(exports, "AuthService", { enumerable: true, get: function () { return auth_service_2.AuthSharedService; } });
//# sourceMappingURL=index.js.map