services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: customer-postgres
    environment:
      POSTGRES_DB: customer_service
      POSTGRES_USER: nestjs
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - customer-network

  # Customer Backend API
  customer-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: customer-backend
    ports:
      - "3060:3060"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************/customer_service?schema=public
      - PORT=3060
      - JWT_SECRET=customer-service-jwt-secret-key-for-development
      - ALLOWED_ORIGIN_1=http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
      - ALLOWED_ORIGIN_2=http://ng-customer-fe-local.dev.dev1.ngnair.com:3061
      - SOURCE_IP=0.0.0.0
      - ALLOWED_ORIGIN_3=http://customer-embed:3002
      - LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev.dev1.ngnair.com:3060
      - LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev.dev1.ngnair.com:3061
      - LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
      - AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
      - AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
      - ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - COOKIE_SECRET=your-secret-key-here
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://ng-customer-admin-local.dev.dev1.ngnair.com:3062,http://ng-customer-fe-local.dev.dev1.ngnair.com:3061
    depends_on:
      - postgres
    networks:
      - customer-network
    volumes:
      - ./backend:/app
    command: sh -c "npx prisma migrate deploy && npm start"

  # Customer Admin Frontend
  customer-admin:
    build:
      context: ./customer-admin
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY: b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
        NEXT_PUBLIC_AUTH_FRONTEND_URL: https://ng-auth-fe-dev.dev1.ngnair.com
        NEXT_PUBLIC_AUTH_JWKS_URL: https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
        NEXT_PUBLIC_CUSTOMER_API_URL: http://ng-customer-local.dev.dev1.ngnair.com:3060
        NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL: http://ng-customer-local.dev.dev1.ngnair.com:3060/graphql

        NEXT_PUBLIC_COOKIE_DOMAIN: .dev1.ngnair.com
        NEXT_PUBLIC_ALLOWED_ORIGINS: http://ng-customer-admin-local.dev.dev1.ngnair.com:3062,http://ng-customer-fe-local.dev.dev1.ngnair.com:3061
    container_name: customer-admin
    ports:
      - "3062:3062"
    environment:
      - NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev.dev1.ngnair.com:3060
      - NEXT_PUBLIC_LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev.dev1.ngnair.com:3061
      - NEXT_PUBLIC_LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
      - NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=http://ng-customer-local.dev.dev1.ngnair.com:3060/graphql
      - NEXT_PUBLIC_CUSTOMER_API_URL=http://ng-customer-local.dev.dev1.ngnair.com:3060
      - NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
      - NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
      - NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - NEXT_PUBLIC_AUTH_DEBUG_MODE=true
      - NEXT_PUBLIC_COOKIE_DOMAIN=.dev1.ngnair.com
      - NEXT_PUBLIC_ALLOWED_ORIGINS=http://ng-customer-admin-local.dev.dev1.ngnair.com:3062,http://ng-customer-fe-local.dev.dev1.ngnair.com:3061
      - PORT=3062
      - HOSTNAME=0.0.0.0
    depends_on:
      - customer-backend
    networks:
      - customer-network

  # Customer Embed Frontend
  customer-embed:
    build:
      context: ./customer-embed
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY: b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
        NEXT_PUBLIC_AUTH_FRONTEND_URL: https://ng-auth-fe-dev.dev1.ngnair.com
        NEXT_PUBLIC_AUTH_JWKS_URL: https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
        NEXT_PUBLIC_CUSTOMER_API_URL: http://ng-customer-local.dev.dev1.ngnair.com:3060
        NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL: http://ng-customer-local.dev.dev1.ngnair.com:3060/graphql
        NEXT_PUBLIC_COOKIE_DOMAIN: .dev1.ngnair.com
        NEXT_PUBLIC_ALLOWED_ORIGINS: http://ng-customer-fe-local.dev.dev1.ngnair.com:3061,http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
    container_name: customer-embed
    ports:
      - "3061:3061"
    environment:
      - NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev.dev1.ngnair.com:3060
      - NEXT_PUBLIC_LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev.dev1.ngnair.com:3061
      - NEXT_PUBLIC_LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
      - NEXT_PUBLIC_CUSTOMER_API_URL=http://ng-customer-local.dev.dev1.ngnair.com:3060
      - NEXT_PUBLIC_BACKEND_URL=http://ng-customer-local.dev.dev1.ngnair.com:3060
      - NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
      - NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
      - NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=http://ng-customer-local.dev.dev1.ngnair.com:3060/graphql
      - NEXT_PUBLIC_COOKIE_DOMAIN=.dev1.ngnair.com
      - NEXT_PUBLIC_ALLOWED_ORIGINS=http://ng-customer-fe-local.dev.dev1.ngnair.com:3061,http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
      - ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - PORT=3061
      - HOSTNAME=0.0.0.0
    depends_on:
      - customer-backend
    networks:
      - customer-network

  # Redis (Optional - for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: customer-redis
    ports:
      - "6379:6379"
    networks:
      - customer-network

volumes:
  postgres_data:

networks:
  customer-network:
    driver: bridge
