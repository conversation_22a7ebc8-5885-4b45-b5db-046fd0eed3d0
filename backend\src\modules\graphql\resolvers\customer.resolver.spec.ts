import { Test, TestingModule } from '@nestjs/testing';
import { CustomerResolver } from './customer.resolver';
import { CustomersQueryService } from '../../customers-query/customers-query.service';
import { CustomersManagementService } from '../../customers-management/customers-management.service';
import { CustomersVerificationService } from '../../customers-verification/customers-verification.service';
import { CustomersAdminService } from '../../customers-admin/customers-admin.service';
import { NotFoundException } from '@nestjs/common';
import { CustomerStatus, CustomerType } from '@prisma/client';

describe('CustomerResolver', () => {
  let resolver: CustomerResolver;

  const mockCustomerService = {
    findMany: jest.fn(),
    findById: jest.fn(),
    findByEmail: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    updateStatus: jest.fn(),
    verifyEmail: jest.fn(),
    verifyPhone: jest.fn(),
  };

  const mockUser = {
    id: 'user-1',
    email: '<EMAIL>',
    isAdmin: false,
    partnerId: 'partner-1',
    permissions: ['read:customers', 'write:customers'],
  };

  const mockCustomer = {
    id: 'customer-1',
    externalId: 'ext-1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    dateOfBirth: new Date('1990-01-01'),
    companyName: null,
    taxId: null,
    businessType: null,
    status: CustomerStatus.ACTIVE,
    type: CustomerType.INDIVIDUAL,
    isEmailVerified: true,
    isPhoneVerified: false,
    isKycVerified: false,
    tags: ['premium'],
    notes: 'Test customer',
    createdAt: new Date(),
    updatedAt: new Date(),
    lastLoginAt: new Date(),
    deletedAt: null,
    addresses: [],
    contacts: [],
    preferences: [],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerResolver,
        {
          provide: CustomerService,
          useValue: mockCustomerService,
        },
      ],
    }).compile();

    resolver = module.get<CustomerResolver>(CustomerResolver);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('customers', () => {
    it('should return list of customers', async () => {
      mockCustomerService.findMany.mockResolvedValue([mockCustomer]);

      const result = await resolver.getCustomers(
        { search: 'john' },
        { skip: 0, take: 10 },
        mockUser,
      );

      expect(result).toEqual([mockCustomer]);
      expect(mockCustomerService.findMany).toHaveBeenCalledWith(
        { search: 'john' },
        { skip: 0, take: 10, orderBy: { createdAt: 'desc' } },
        mockUser,
      );
    });

    it('should handle empty filters', async () => {
      mockCustomerService.findMany.mockResolvedValue([mockCustomer]);

      const result = await resolver.getCustomers(undefined, undefined, mockUser);

      expect(result).toEqual([mockCustomer]);
      expect(mockCustomerService.findMany).toHaveBeenCalledWith(
        undefined,
        { orderBy: { createdAt: 'desc' } },
        mockUser,
      );
    });
  });

  describe('customer', () => {
    it('should return customer by id', async () => {
      mockCustomerService.findById.mockResolvedValue(mockCustomer);

      const result = await resolver.getCustomer('customer-1', mockUser);

      expect(result).toEqual(mockCustomer);
      expect(mockCustomerService.findById).toHaveBeenCalledWith('customer-1', mockUser);
    });

    it('should throw NotFoundException when customer not found', async () => {
      mockCustomerService.findById.mockRejectedValue(new NotFoundException());

      await expect(resolver.getCustomer('non-existent', mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('customerByEmail', () => {
    it('should return customer by email', async () => {
      mockCustomerService.findByEmail.mockResolvedValue(mockCustomer);

      const result = await resolver.getCustomerByEmail('<EMAIL>', mockUser);

      expect(result).toEqual(mockCustomer);
      expect(mockCustomerService.findByEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        mockUser,
      );
    });

    it('should return null when customer not found', async () => {
      mockCustomerService.findByEmail.mockResolvedValue(null);

      const result = await resolver.getCustomerByEmail(
        '<EMAIL>',
        mockUser,
      );

      expect(result).toBeNull();
    });
  });

  describe('searchCustomers', () => {
    it('should perform advanced search', async () => {
      mockCustomerService.findMany.mockResolvedValue([mockCustomer]);

      const result = await resolver.searchCustomers(
        { search: 'john', status: CustomerStatus.ACTIVE },
        { skip: 0, take: 10 },
        mockUser,
      );

      expect(result).toEqual([mockCustomer]);
      expect(mockCustomerService.findMany).toHaveBeenCalledWith(
        { search: 'john', status: CustomerStatus.ACTIVE },
        { skip: 0, take: 10, orderBy: { createdAt: 'desc' } },
        mockUser,
      );
    });
  });

  describe('createCustomer', () => {
    const createInput = {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+1987654321',
      status: CustomerStatus.ACTIVE,
      type: CustomerType.INDIVIDUAL,
    };

    it('should create customer successfully', async () => {
      mockCustomerService.create.mockResolvedValue(mockCustomer);

      const result = await resolver.createCustomer(createInput, mockUser);

      expect(result).toEqual(mockCustomer);
      expect(mockCustomerService.create).toHaveBeenCalledWith(createInput, mockUser);
    });
  });

  describe('updateCustomer', () => {
    const updateInput = {
      firstName: 'John Updated',
      phone: '+1111111111',
    };

    it('should update customer successfully', async () => {
      const updatedCustomer = { ...mockCustomer, ...updateInput };
      mockCustomerService.update.mockResolvedValue(updatedCustomer);

      const result = await resolver.updateCustomer({ id: 'customer-1', ...updateInput }, mockUser);

      expect(result).toEqual(updatedCustomer);
      expect(mockCustomerService.update).toHaveBeenCalledWith(
        'customer-1',
        updateInput,
        mockUser,
      );
    });
  });

  describe('deleteCustomer', () => {
    it('should delete customer successfully', async () => {
      mockCustomerService.delete.mockResolvedValue(true);

      const result = await resolver.deleteCustomer('customer-1', mockUser);

      expect(result).toBe(true);
      expect(mockCustomerService.delete).toHaveBeenCalledWith('customer-1', mockUser);
    });
  });

  describe('updateCustomerStatus', () => {
    it('should update customer status successfully', async () => {
      const updatedCustomer = { ...mockCustomer, status: CustomerStatus.SUSPENDED };
      mockCustomerService.updateStatus.mockResolvedValue(updatedCustomer);

      const result = await resolver.updateCustomerStatus(
        'customer-1',
        CustomerStatus.SUSPENDED,
        mockUser,
      );

      expect(result).toEqual(updatedCustomer);
      expect(mockCustomerService.updateStatus).toHaveBeenCalledWith(
        'customer-1',
        CustomerStatus.SUSPENDED,
        mockUser,
      );
    });
  });

  describe('verifyCustomerEmail', () => {
    it('should verify customer email successfully', async () => {
      const verifiedCustomer = { ...mockCustomer, isEmailVerified: true };
      mockCustomerService.verifyEmail.mockResolvedValue(verifiedCustomer);

      const result = await resolver.verifyCustomerEmail('customer-1', mockUser);

      expect(result).toEqual(verifiedCustomer);
      expect(mockCustomerService.verifyEmail).toHaveBeenCalledWith('customer-1', mockUser);
    });
  });

  describe('verifyCustomerPhone', () => {
    it('should verify customer phone successfully', async () => {
      const verifiedCustomer = { ...mockCustomer, isPhoneVerified: true };
      mockCustomerService.verifyPhone.mockResolvedValue(verifiedCustomer);

      const result = await resolver.verifyCustomerPhone('customer-1', mockUser);

      expect(result).toEqual(verifiedCustomer);
      expect(mockCustomerService.verifyPhone).toHaveBeenCalledWith('customer-1', mockUser);
    });
  });
});
