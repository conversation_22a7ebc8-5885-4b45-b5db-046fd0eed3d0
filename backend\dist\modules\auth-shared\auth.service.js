"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthSharedService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthSharedService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_service_1 = require("../../config/config.service");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const rxjs_1 = require("rxjs");
let AuthSharedService = AuthSharedService_1 = class AuthSharedService {
    configService;
    httpService;
    logger = new common_1.Logger(AuthSharedService_1.name);
    config;
    constructor(configService, httpService) {
        this.configService = configService;
        this.httpService = httpService;
        this.config = {
            authJwksUrl: this.configService.get('AUTH_JWKS_URL') || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
            encryptionKey: this.configService.get('ACCESS_TOKEN_ENCRYPTION_KEY') || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
            cookieNames: {
                accessToken: 'access_token',
                refreshToken: 'refresh_token',
            },
        };
    }
    decryptCookie(encryptedValue) {
        try {
            this.logger.log(`Attempting to decrypt token: ${encryptedValue.substring(0, 50)}...`);
            const decodedValue = decodeURIComponent(encryptedValue);
            this.logger.log(`URL decoded length: ${decodedValue.length}`);
            const parts = decodedValue.split('--');
            this.logger.log(`Split into ${parts.length} parts`);
            if (parts.length !== 3) {
                this.logger.error(`Invalid encrypted cookie format. Expected 3 parts, got ${parts.length}`);
                throw new Error('Invalid encrypted cookie format');
            }
            const encryptedData = Buffer.from(parts[0], 'base64');
            const iv = Buffer.from(parts[1], 'base64');
            const authTag = Buffer.from(parts[2], 'base64');
            const key = Buffer.from(this.config.encryptionKey, 'hex');
            const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
            decipher.setAuthTag(authTag);
            let decrypted = decipher.update(encryptedData, undefined, 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted.replace(/^"(.*)"$/, '$1');
        }
        catch (error) {
            this.logger.error('Failed to decrypt cookie:', error);
            throw new common_1.UnauthorizedException('Invalid encrypted token');
        }
    }
    extractRawTokensFromCookies(cookies) {
        const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
        const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];
        this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
        this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
        this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
        this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
        this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);
        return {
            accessToken: encryptedAccessToken,
            refreshToken: encryptedRefreshToken
        };
    }
    extractTokensFromCookies(cookies) {
        const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
        const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];
        this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
        this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
        this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
        this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
        this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);
        if (!encryptedAccessToken) {
            throw new common_1.UnauthorizedException('Missing access token');
        }
        if (!encryptedRefreshToken) {
            this.logger.warn('Refresh token not found, proceeding with access token only');
        }
        try {
            const accessToken = this.decryptCookie(encryptedAccessToken);
            const refreshToken = encryptedRefreshToken ? this.decryptCookie(encryptedRefreshToken) : undefined;
            return { accessToken, refreshToken };
        }
        catch (error) {
            this.logger.error('Failed to decrypt tokens:', error);
            throw new common_1.UnauthorizedException('Invalid authentication tokens');
        }
    }
    async verifyToken(token) {
        try {
            const decoded = jwt.decode(token, { complete: true });
            if (!decoded || !decoded.header.kid) {
                throw new common_1.UnauthorizedException('Invalid token format');
            }
            const payload = decoded.payload;
            return payload;
        }
        catch (error) {
            this.logger.error('Token verification failed:', error);
            throw new common_1.UnauthorizedException('Invalid or expired token');
        }
    }
    getUserFromPayload(payload) {
        const role = payload.role || 'admin';
        this.logger.log(`📋 [AUTH SERVICE] User role from JWT: ${payload.role || 'undefined'}, using: ${role}`);
        return {
            id: payload.sub,
            email: payload.email,
            username: payload.username,
            firstName: payload.firstName,
            lastName: payload.lastName,
            role: role,
            permissions: payload.permissions || [],
            createdAt: new Date(payload.iat * 1000).toISOString(),
            updatedAt: new Date().toISOString(),
        };
    }
    async authenticateFromCookies(cookies) {
        this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies');
        this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);
        try {
            this.logger.log('🔓 [AUTH SERVICE] Extracting and decrypting tokens...');
            const { accessToken } = this.extractTokensFromCookies(cookies);
            this.logger.log(`🔑 [AUTH SERVICE] Access token extracted: ${accessToken ? 'YES' : 'NO'}`);
            this.logger.log('✅ [AUTH SERVICE] Verifying access token...');
            const payload = await this.verifyToken(accessToken);
            this.logger.log(`👤 [AUTH SERVICE] Token verified, user ID: ${payload.sub}`);
            this.logger.log('📋 [AUTH SERVICE] Getting user info from payload...');
            const user = this.getUserFromPayload(payload);
            this.logger.log(`✅ [AUTH SERVICE] Authentication successful: ${user.email} (${user.role})`);
            return user;
        }
        catch (error) {
            this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
            this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
    async refreshAuthentication(cookies) {
        try {
            const { refreshToken } = this.extractTokensFromCookies(cookies);
            if (!refreshToken) {
                throw new common_1.UnauthorizedException('Refresh token not available');
            }
            await this.verifyToken(refreshToken);
            return true;
        }
        catch (error) {
            this.logger.error('Token refresh failed:', error);
            return false;
        }
    }
    hasPermissions(user, requiredPermissions) {
        if (!user.permissions)
            return false;
        return requiredPermissions.every(permission => user.permissions.includes(permission));
    }
    hasRole(user, requiredRole) {
        return user.role === requiredRole;
    }
    async authenticateFromCookiesViaExternalService(cookies) {
        this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies via external service');
        this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);
        try {
            const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);
            if (!encryptedAccessToken) {
                throw new common_1.UnauthorizedException('Missing access token');
            }
            const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
            const url = `${authServiceUrl}/api/v1/users`;
            this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
            this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);
            const requestHeaders = {
                'Cookie': `access_token=${encryptedAccessToken}`,
                'Content-Type': 'application/json',
                'User-Agent': 'Customer-Service/1.0',
            };
            this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
            this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
            this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
            this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
            this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(url, {
                headers: requestHeaders,
                timeout: 10000,
            }));
            this.logger.log(`✅ [AUTH SERVICE] User authenticated via external service`);
            this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);
            const userData = Array.isArray(response.data) ? response.data[0] : response.data;
            return userData;
        }
        catch (error) {
            this.logger.error(`❌ [AUTH SERVICE] External authentication failed: ${error.message}`);
            if (error.response?.status === 401) {
                throw new common_1.UnauthorizedException('Invalid or expired access token');
            }
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
    setAccessTokenCookie(response, token) {
        try {
            this.logger.log(`🍪 [AUTH SERVICE] Setting access token cookie manually`);
            this.logger.log(`🔐 [AUTH SERVICE] Token length: ${token.length} characters`);
            this.logger.log(`🔐 [AUTH SERVICE] Token preview: ${token.substring(0, 50)}...`);
            const encodedToken = encodeURIComponent(token);
            this.logger.log(`🔗 [AUTH SERVICE] URL encoded token length: ${encodedToken.length} characters`);
            const cookieOptions = [
                `access_token=${encodedToken}`,
                'Domain=.dev1.ngnair.com',
                'Path=/',
                'Max-Age=900',
                'SameSite=Lax'
            ].join('; ');
            response.setHeader('Set-Cookie', cookieOptions);
            this.logger.log(`✅ [AUTH SERVICE] Cookie set successfully: ${cookieOptions.substring(0, 100)}...`);
            return true;
        }
        catch (error) {
            this.logger.error(`❌ [AUTH SERVICE] Failed to set cookie: ${error.message}`);
            return false;
        }
    }
    async decryptTokenForTesting(encryptedToken) {
        return this.decryptCookie(encryptedToken);
    }
    async getUserById(userId, cookies) {
        try {
            this.logger.log(`🔍 [AUTH SERVICE] Getting user by ID: ${userId}`);
            const currentUser = await this.authenticateFromCookies(cookies);
            this.logger.log(`👤 [AUTH SERVICE] Current user authenticated: ${currentUser.email}`);
            if (currentUser.id === userId) {
                this.logger.log(`✅ [AUTH SERVICE] User requesting own profile, returning current user data`);
                return currentUser;
            }
            this.logger.warn(`⚠️ [AUTH SERVICE] User ${currentUser.email} attempted to access profile of ${userId}`);
            throw new common_1.UnauthorizedException('You can only access your own profile');
        }
        catch (error) {
            this.logger.error(`❌ [AUTH SERVICE] Failed to get user by ID: ${error.message}`);
            throw new common_1.UnauthorizedException('Failed to retrieve user information');
        }
    }
    async getAllUsers(cookies) {
        this.logger.log('🔍 [AUTH SERVICE] Getting all users from external service');
        this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);
        try {
            const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);
            if (!encryptedAccessToken) {
                throw new common_1.UnauthorizedException('Missing access token');
            }
            const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
            const url = `${authServiceUrl}/api/v1/users`;
            this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
            this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);
            const requestHeaders = {
                'Cookie': `access_token=${encryptedAccessToken}`,
                'Content-Type': 'application/json',
                'User-Agent': 'Customer-Service/1.0',
            };
            this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
            this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
            this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
            this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
            this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(url, {
                headers: requestHeaders,
                timeout: 10000,
            }));
            this.logger.log(`✅ [AUTH SERVICE] Users retrieved from external service`);
            this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);
            const usersData = Array.isArray(response.data) ? response.data : [response.data];
            return usersData;
        }
        catch (error) {
            this.logger.error(`❌ [AUTH SERVICE] Failed to get all users from external service: ${error.message}`);
            if (error.response?.status === 401) {
                throw new common_1.UnauthorizedException('Invalid or expired access token');
            }
            throw new common_1.UnauthorizedException('Failed to retrieve users information');
        }
    }
};
exports.AuthSharedService = AuthSharedService;
exports.AuthSharedService = AuthSharedService = AuthSharedService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService,
        axios_1.HttpService])
], AuthSharedService);
//# sourceMappingURL=auth.service.js.map