import { Controller, Post, Get, Delete, Body, Param, Req, Logger, HttpStatus, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { FastifyRequest } from 'fastify';
import { PaymentTokensService } from './payment-tokens.service';
import { NearPayWebhookDto, PaymentTokenResponseDto } from './dto/nearpay-webhook.dto';

@ApiTags('Payment Tokens')
@Controller('payment-tokens')
export class PaymentTokensController {
  private readonly logger = new Logger(PaymentTokensController.name);

  constructor(private readonly paymentTokensService: PaymentTokensService) {}

  @Post('webhooks/nearpay')
  @ApiOperation({ 
    summary: 'NearPay webhook endpoint',
    description: 'Receives transaction data from NearPay and creates/updates payment tokens for customers'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Payment token created/updated successfully',
    type: PaymentTokenResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid webhook data or non-approved transaction',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  async handleNearPayWebhook(
    @Body() webhookData: NearPayWebhookDto,
    @Req() request: FastifyRequest,
  ): Promise<PaymentTokenResponseDto> {
    this.logger.log(`Received NearPay webhook for transaction: ${webhookData.transactionId}`);
    
    // Get client IP for audit trail
    const clientIp = request.ip || request.headers['x-forwarded-for'] as string || 'unknown';
    
    // Log webhook data for debugging (remove sensitive info in production)
    this.logger.debug(`Webhook data: ${JSON.stringify({
      transactionId: webhookData.transactionId,
      amount: webhookData.amount,
      currency: webhookData.currency,
      status: webhookData.status,
      cardLast4: webhookData.card.last4,
      cardBrand: webhookData.card.brand,
      merchantId: webhookData.merchant.merchantId,
      customerEmail: webhookData.customerEmail,
      customerPhone: webhookData.customerPhone,
    })}`);

    return await this.paymentTokensService.processNearPayWebhook(webhookData, clientIp);
  }

  @Get('customer/:customerId')
  @ApiOperation({ 
    summary: 'Get customer payment tokens',
    description: 'Retrieve all active payment tokens for a specific customer'
  })
  @ApiParam({ name: 'customerId', description: 'Customer UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer payment tokens retrieved successfully',
    type: [PaymentTokenResponseDto],
  })
  async getCustomerPaymentTokens(
    @Param('customerId', ParseUUIDPipe) customerId: string,
  ): Promise<PaymentTokenResponseDto[]> {
    this.logger.log(`Retrieving payment tokens for customer: ${customerId}`);
    return await this.paymentTokensService.getCustomerPaymentTokens(customerId);
  }

  @Delete(':tokenId/customer/:customerId')
  @ApiOperation({ 
    summary: 'Revoke payment token',
    description: 'Revoke a specific payment token for a customer'
  })
  @ApiParam({ name: 'tokenId', description: 'Payment token UUID' })
  @ApiParam({ name: 'customerId', description: 'Customer UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Payment token revoked successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Payment token not found',
  })
  async revokePaymentToken(
    @Param('tokenId', ParseUUIDPipe) tokenId: string,
    @Param('customerId', ParseUUIDPipe) customerId: string,
  ): Promise<void> {
    this.logger.log(`Revoking payment token: ${tokenId} for customer: ${customerId}`);
    await this.paymentTokensService.revokePaymentToken(tokenId, customerId);
  }

  @Post('webhooks/nearpay/test')
  @ApiOperation({
    summary: 'Test NearPay webhook (Development only)',
    description: 'Test endpoint to simulate NearPay webhook calls for development and testing'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Test webhook processed successfully',
    type: PaymentTokenResponseDto,
  })
  async testNearPayWebhook(@Req() request: FastifyRequest): Promise<PaymentTokenResponseDto> {
    // Sample test data - in production this endpoint should be removed or protected
    const testWebhookData: NearPayWebhookDto = {
      transactionId: `test_${Date.now()}`,
      referenceNumber: `REF${Date.now()}`,
      amount: 10000, // $100.00 in cents
      currency: 'USD',
      status: 'APPROVED' as any,
      type: 'SALE' as any,
      authCode: 'AUTH123',
      card: {
        last4: '1234',
        brand: 'Visa',
        cardType: 'Credit',
        expiryMonth: '12',
        expiryYear: '2025',
        cardholderName: 'John Doe',
      },
      merchant: {
        merchantId: 'MERCHANT_001',
        terminalId: 'TERMINAL_001',
        merchantName: 'Test Merchant',
      },
      timestamp: new Date().toISOString(),
      customerEmail: '<EMAIL>', // This should match a seeded customer
      eventType: 'transaction.approved',
    };

    this.logger.log('Processing test NearPay webhook');
    const clientIp = request.ip || 'test';

    return await this.paymentTokensService.processNearPayWebhook(testWebhookData, clientIp);
  }

  @Get('validate/timestamp')
  @ApiOperation({
    summary: 'Test timestamp validation',
    description: 'Test endpoint to validate timestamp format'
  })
  async testTimestampValidation(): Promise<{ message: string; examples: any[] }> {
    const examples = [
      { format: 'ISO 8601 with Z', value: new Date().toISOString(), valid: true },
      { format: 'ISO 8601 without Z', value: new Date().toISOString().slice(0, -1), valid: true },
      { format: 'Date string', value: new Date().toString(), valid: false },
      { format: 'Unix timestamp', value: Date.now().toString(), valid: false },
    ];

    return {
      message: 'Timestamp validation examples',
      examples
    };
  }
}
