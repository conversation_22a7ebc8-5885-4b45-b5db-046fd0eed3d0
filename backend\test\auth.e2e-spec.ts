import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Auth API (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/auth/users (GET)', () => {
    it('should return 401 when no access token is provided', async () => {
      const response = await request(app.getHttpServer())
        .get('/auth/users')
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
      expect(response.body).toHaveProperty('message');
    });

    it('should return 401 when invalid access token is provided', async () => {
      const response = await request(app.getHttpServer())
        .get('/auth/users')
        .set('Cookie', 'access_token=invalid_token')
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
      expect(response.body).toHaveProperty('message');
    });

    // Note: Testing with valid tokens would require setting up proper test tokens
    // or mocking the external auth service, which is beyond the scope of this fix
  });

  describe('/auth/status (GET)', () => {
    it('should return service status', async () => {
      const response = await request(app.getHttpServer())
        .get('/auth/status')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('service', 'auth-shared');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('version');
    });
  });
});
