import axios from 'axios';
import { CustomerFormData, CustomerResponse, TaxCalculationRequest, TaxCalculationResponse } from '@/types/customer';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export class CustomerAPI {
  /**
   * Create a new customer
   */
  static async createCustomer(customerData: CustomerFormData): Promise<CustomerResponse> {
    try {
      const response = await api.post('/customers', {
        firstName: customerData.firstName,
        lastName: customerData.lastName,
        email: customerData.email,
        phone: customerData.phone,
        status: 'ACTIVE',
        type: 'INDIVIDUAL',
        addresses: [{
          type: 'BILLING',
          street: customerData.address,
          city: customerData.city,
          state: customerData.state,
          zipCode: customerData.zipCode,
          country: customerData.country,
          isDefault: true,
        }],
      });
      
      return response.data;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw new Error('Failed to create customer');
    }
  }

  /**
   * Update an existing customer
   */
  static async updateCustomer(customerId: string, customerData: Partial<CustomerFormData>): Promise<CustomerResponse> {
    try {
      const response = await api.put(`/customers/${customerId}`, customerData);
      return response.data;
    } catch (error) {
      console.error('Error updating customer:', error);
      throw new Error('Failed to update customer');
    }
  }

  /**
   * Get customer by ID
   */
  static async getCustomer(customerId: string): Promise<CustomerResponse> {
    try {
      const response = await api.get(`/customers/${customerId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching customer:', error);
      throw new Error('Failed to fetch customer');
    }
  }

  /**
   * Get all customers
   */
  static async getAllCustomers(): Promise<CustomerResponse[]> {
    try {
      const response = await api.get('/customers');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw new Error('Failed to fetch customers');
    }
  }

  /**
   * Calculate tax for a given address and amount
   * This would integrate with Numeral or another tax service
   */
  static async calculateTax(request: TaxCalculationRequest): Promise<TaxCalculationResponse> {
    try {
      // For now, return mock tax calculation
      // In production, this would call the actual tax service
      const mockTaxRate = 0.08; // 8% tax rate
      const taxAmount = request.amount * mockTaxRate;
      
      return {
        taxAmount,
        taxRate: mockTaxRate,
        totalAmount: request.amount + taxAmount,
        breakdown: {
          stateTax: taxAmount * 0.6,
          localTax: taxAmount * 0.3,
          federalTax: taxAmount * 0.1,
        },
      };
    } catch (error) {
      console.error('Error calculating tax:', error);
      throw new Error('Failed to calculate tax');
    }
  }
}

export default api;
