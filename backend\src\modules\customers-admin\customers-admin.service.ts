import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Customer, CustomerStatus, AuditAction } from '@prisma/client';
import { CustomerWithRelations } from '../customers-query/customers-query.service';

// Re-export for convenience
export { CustomerWithRelations } from '../customers-query/customers-query.service';

@Injectable()
export class CustomersAdminService {
  private readonly logger = new Logger(CustomersAdminService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Suspend a customer
   */
  async suspendCustomer(customerId: string): Promise<CustomerWithRelations> {
    this.logger.log(`Suspending customer ${customerId}`);

    const customer = await this.prisma.customer.update({
      where: { id: customerId },
      data: {
        status: CustomerStatus.SUSPENDED,
        updatedAt: new Date(),
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Log the suspension action
    await this.prisma.auditLog.create({
      data: {
        customerId,
        action: AuditAction.STATUS_CHANGE,
        entity: 'customer',
        entityId: customerId,
        description: `Customer suspended by admin`,
        metadata: { suspendedAt: new Date(), reason: 'Administrative action' },
      },
    });

    return customer;
  }

  /**
   * Activate a customer
   */
  async activateCustomer(customerId: string): Promise<CustomerWithRelations> {
    this.logger.log(`Activating customer ${customerId}`);

    const customer = await this.prisma.customer.update({
      where: { id: customerId },
      data: {
        status: CustomerStatus.ACTIVE,
        updatedAt: new Date(),
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Log the activation action
    await this.prisma.auditLog.create({
      data: {
        customerId,
        action: AuditAction.STATUS_CHANGE,
        entity: 'customer',
        entityId: customerId,
        description: `Customer activated by admin`,
        metadata: { activatedAt: new Date() },
      },
    });

    return customer;
  }

  /**
   * Block a customer
   */
  async blockCustomer(customerId: string): Promise<CustomerWithRelations> {
    this.logger.log(`Blocking customer ${customerId}`);

    const customer = await this.prisma.customer.update({
      where: { id: customerId },
      data: {
        status: CustomerStatus.BLOCKED,
        updatedAt: new Date(),
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Log the blocking action
    await this.prisma.auditLog.create({
      data: {
        customerId,
        action: AuditAction.STATUS_CHANGE,
        entity: 'customer',
        entityId: customerId,
        description: `Customer blocked by admin`,
        metadata: { blockedAt: new Date(), reason: 'Administrative action' },
      },
    });

    return customer;
  }

  /**
   * Admin update customer (with elevated permissions)
   */
  async adminUpdateCustomer(
    customerId: string,
    updateData: Partial<Customer>,
  ): Promise<CustomerWithRelations> {
    this.logger.log(`Admin updating customer ${customerId}`);

    const customer = await this.prisma.customer.update({
      where: { id: customerId },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Log the admin update action
    await this.prisma.auditLog.create({
      data: {
        customerId,
        action: AuditAction.UPDATE,
        entity: 'customer',
        entityId: customerId,
        description: `Customer updated by admin`,
        metadata: {
          updatedFields: Object.keys(updateData),
          updatedAt: new Date(),
        },
      },
    });

    return customer;
  }
}
