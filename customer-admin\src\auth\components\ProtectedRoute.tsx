import React, { ReactNode, useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { authService } from '../services/authService';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  requiredPermissions?: string[];
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermissions,
  fallback = <div>Loading...</div>
}) => {
  const { user, isAuthenticated, isLoading, login } = useAuth();
  const [hasCheckedCookies, setHasCheckedCookies] = useState(false);

  // Check for cookies before auto-redirecting (only when not authenticated)
  useEffect(() => {
    if (!isLoading && !isAuthenticated && !hasCheckedCookies) {
      // Check if user has cookies before redirecting
      const hasCookies = authService.hasAuthCookies();
      console.log('🍪 ProtectedRoute - Has cookies:', hasCookies);

      if (!hasCookies) {
        console.log('🔐 ProtectedRoute - No cookies found, redirecting to login');
        login();
      } else {
        console.log('🔍 ProtectedRoute - Cookies found, waiting for AuthProvider to authenticate');
      }

      setHasCheckedCookies(true);
    } else if (isAuthenticated) {
      // If user is authenticated, we don't need to check cookies
      setHasCheckedCookies(true);
    }
  }, [isLoading, isAuthenticated, hasCheckedCookies, login]);

  // Show loading state
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Show loading while checking cookies
  if (!hasCheckedCookies) {
    return <>{fallback}</>;
  }

  // Show redirecting message if not authenticated and no cookies
  if (!isAuthenticated || !user) {
    const hasCookies = authService.hasAuthCookies();
    if (!hasCookies) {
      return <div>Redirecting to login...</div>;
    } else {
      return <div>Authenticating...</div>;
    }
  }

  // Check role requirements
  if (requiredRole && user.role !== requiredRole) {
    return <div>Access denied: Insufficient role permissions</div>;
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasPermissions = requiredPermissions.every(permission => 
      user.permissions?.includes(permission)
    );
    
    if (!hasPermissions) {
      return <div>Access denied: Insufficient permissions</div>;
    }
  }

  return <>{children}</>;
};
