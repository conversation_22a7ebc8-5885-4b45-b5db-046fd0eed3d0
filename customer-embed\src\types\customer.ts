export interface CustomerFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface Address {
  id: string;
  type: string;
  label?: string;
  street1: string;
  street2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerResponse {
  id: string;
  externalId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  companyName?: string;
  taxId?: string;
  businessType?: string;
  status: string;
  type: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isKycVerified: boolean;
  tags: string[];
  notes?: string;
  addresses?: Address[];
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  deletedAt?: string;
}

export interface TaxCalculationRequest {
  amount: number;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface TaxCalculationResponse {
  taxAmount: number;
  taxRate: number;
  totalAmount: number;
  breakdown: {
    stateTax: number;
    localTax: number;
    federalTax: number;
  };
}

export interface EmbedConfig {
  apiBaseUrl?: string;
  onCustomerCreated?: (customer: CustomerResponse) => void;
  onTaxCalculated?: (tax: TaxCalculationResponse) => void;
  onError?: (error: string) => void;
  theme?: {
    primaryColor?: string;
    borderRadius?: string;
    fontFamily?: string;
  };
}
