
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  externalId: 'externalId',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  dateOfBirth: 'dateOfBirth',
  companyName: 'companyName',
  taxId: 'taxId',
  businessType: 'businessType',
  status: 'status',
  type: 'type',
  isEmailVerified: 'isEmailVerified',
  isPhoneVerified: 'isPhoneVerified',
  isKycVerified: 'isKycVerified',
  tags: 'tags',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLoginAt: 'lastLoginAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.AddressScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  type: 'type',
  label: 'label',
  street1: 'street1',
  street2: 'street2',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  latitude: 'latitude',
  longitude: 'longitude',
  isDefault: 'isDefault',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ContactScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  type: 'type',
  label: 'label',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  relationship: 'relationship',
  isDefault: 'isDefault',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomerPreferenceScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  type: 'type',
  key: 'key',
  value: 'value',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  action: 'action',
  entity: 'entity',
  entityId: 'entityId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  sessionId: 'sessionId',
  description: 'description',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.CustomerSegmentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  criteria: 'criteria',
  isActive: 'isActive',
  isAutomatic: 'isAutomatic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomerSegmentMemberScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  segmentId: 'segmentId',
  assignedAt: 'assignedAt',
  assignedBy: 'assignedBy',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentTokenScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  tokenHash: 'tokenHash',
  externalTokenId: 'externalTokenId',
  paymentProvider: 'paymentProvider',
  tokenType: 'tokenType',
  status: 'status',
  maskedInfo: 'maskedInfo',
  paymentBrand: 'paymentBrand',
  expiresAt: 'expiresAt',
  elavonToken: 'elavonToken',
  elavonTransactionId: 'elavonTransactionId',
  elavonReferenceId: 'elavonReferenceId',
  elavonCardType: 'elavonCardType',
  elavonApprovalCode: 'elavonApprovalCode',
  tsepToken: 'tsepToken',
  tsepTransactionId: 'tsepTransactionId',
  tsepTransactionKey: 'tsepTransactionKey',
  tsepDeviceId: 'tsepDeviceId',
  tsepCardType: 'tsepCardType',
  tsepResponseCode: 'tsepResponseCode',
  transactionAmount: 'transactionAmount',
  currency: 'currency',
  customerName: 'customerName',
  customerEmail: 'customerEmail',
  rawWebhookData: 'rawWebhookData',
  providerMetadata: 'providerMetadata',
  financeId: 'financeId',
  accountId: 'accountId',
  createdByIp: 'createdByIp',
  lastUsedAt: 'lastUsedAt',
  usageCount: 'usageCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.CustomerStatus = exports.$Enums.CustomerStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
  PENDING_VERIFICATION: 'PENDING_VERIFICATION',
  BLOCKED: 'BLOCKED'
};

exports.CustomerType = exports.$Enums.CustomerType = {
  INDIVIDUAL: 'INDIVIDUAL',
  BUSINESS: 'BUSINESS',
  ENTERPRISE: 'ENTERPRISE'
};

exports.AddressType = exports.$Enums.AddressType = {
  HOME: 'HOME',
  WORK: 'WORK',
  BILLING: 'BILLING',
  SHIPPING: 'SHIPPING',
  OTHER: 'OTHER'
};

exports.ContactType = exports.$Enums.ContactType = {
  PRIMARY: 'PRIMARY',
  SECONDARY: 'SECONDARY',
  EMERGENCY: 'EMERGENCY',
  BUSINESS: 'BUSINESS'
};

exports.PreferenceType = exports.$Enums.PreferenceType = {
  COMMUNICATION: 'COMMUNICATION',
  NOTIFICATION: 'NOTIFICATION',
  PRIVACY: 'PRIVACY',
  MARKETING: 'MARKETING',
  LANGUAGE: 'LANGUAGE',
  TIMEZONE: 'TIMEZONE'
};

exports.AuditAction = exports.$Enums.AuditAction = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  STATUS_CHANGE: 'STATUS_CHANGE',
  VERIFICATION: 'VERIFICATION'
};

exports.PaymentProvider = exports.$Enums.PaymentProvider = {
  ELAVON: 'ELAVON',
  TSEP: 'TSEP',
  NEARPAY: 'NEARPAY',
  STRIPE: 'STRIPE',
  SQUARE: 'SQUARE',
  OTHER: 'OTHER'
};

exports.PaymentTokenType = exports.$Enums.PaymentTokenType = {
  CARD: 'CARD',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
  DIGITAL_WALLET: 'DIGITAL_WALLET',
  TAP_TO_PAY: 'TAP_TO_PAY'
};

exports.PaymentTokenStatus = exports.$Enums.PaymentTokenStatus = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  REVOKED: 'REVOKED',
  SUSPENDED: 'SUSPENDED'
};

exports.Prisma.ModelName = {
  Customer: 'Customer',
  Address: 'Address',
  Contact: 'Contact',
  CustomerPreference: 'CustomerPreference',
  AuditLog: 'AuditLog',
  CustomerSegment: 'CustomerSegment',
  CustomerSegmentMember: 'CustomerSegmentMember',
  PaymentToken: 'PaymentToken'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
