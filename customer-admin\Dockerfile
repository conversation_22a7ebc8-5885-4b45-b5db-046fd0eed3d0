# === Stage 1: Builder ===
FROM node:20-alpine AS builder

WORKDIR /app

# Install deps only
COPY package.json package-lock.json ./
RUN npm install

# Copy full source
COPY . .

# Build arguments for environment variables (passed from CapRover)
ARG NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY
ARG NEXT_PUBLIC_AUTH_FRONTEND_URL
ARG NEXT_PUBLIC_AUTH_JWKS_URL
ARG NEXT_PUBLIC_CUSTOMER_API_URL
ARG NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_SITE_URL
ARG NEXT_PUBLIC_ADMIN_EMAIL
ARG NEXT_PUBLIC_IMAGE_DOMAINS

# Set environment variables for build (with fallbacks for local development)
ENV NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=${NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY:-b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8}
ENV NEXT_PUBLIC_AUTH_FRONTEND_URL=${NEXT_PUBLIC_AUTH_FRONTEND_URL:-https://ng-auth-fe-dev.dev1.ngnair.com}
ENV NEXT_PUBLIC_AUTH_JWKS_URL=${NEXT_PUBLIC_AUTH_JWKS_URL:-https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks}
ENV NEXT_PUBLIC_CUSTOMER_API_URL=${NEXT_PUBLIC_CUSTOMER_API_URL:-https://ng-customer-dev.dev1.ngnair.com}
ENV NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=${NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL:-https://ng-customer-dev.dev1.ngnair.com/graphql}
ENV NEXT_PUBLIC_APP_NAME="${NEXT_PUBLIC_APP_NAME:-Customer Management System}"
ENV NEXT_PUBLIC_SITE_URL=${NEXT_PUBLIC_SITE_URL:-https://ng-customer-admin-dev.dev1.ngnair.com}
ENV NEXT_PUBLIC_ADMIN_EMAIL=${NEXT_PUBLIC_ADMIN_EMAIL:-<EMAIL>}
ENV NEXT_PUBLIC_IMAGE_DOMAINS=${NEXT_PUBLIC_IMAGE_DOMAINS:-ng-customer-dev.dev1.ngnair.com}

# Build the Next.js app
RUN npm run build

# === Stage 2: Production Image ===
FROM node:20-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production

# Runtime environment variables (these will be available from CapRover)
ENV NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=""
ENV NEXT_PUBLIC_AUTH_FRONTEND_URL=""
ENV NEXT_PUBLIC_AUTH_JWKS_URL=""
ENV NEXT_PUBLIC_CUSTOMER_API_URL=""
ENV NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=""
ENV NEXT_PUBLIC_APP_NAME=""
ENV NEXT_PUBLIC_SITE_URL=""
ENV NEXT_PUBLIC_ADMIN_EMAIL=""
ENV NEXT_PUBLIC_IMAGE_DOMAINS=""

# Copy the minimal production artifacts from the builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/tailwind.config.js ./tailwind.config.js
COPY --from=builder /app/postcss.config.js ./postcss.config.js

EXPOSE 3062

# Start the app
CMD ["npm", "start"]