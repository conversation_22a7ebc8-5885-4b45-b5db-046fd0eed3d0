{"name": "customer-embed", "version": "1.0.0", "description": "Embeddable customer data collection component for finance integration", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "export": "next export"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.45.0", "axios": "^1.5.0", "@apollo/client": "^3.8.0", "graphql": "^16.8.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "clsx": "^2.0.0", "lucide-react": "^0.263.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}