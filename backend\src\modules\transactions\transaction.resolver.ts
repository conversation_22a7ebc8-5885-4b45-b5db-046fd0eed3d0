import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { UseGuards, Logger } from '@nestjs/common';
import { ApiGuard, getCurrentUser, AuthUser } from '../auth-shared';
import { TransactionService } from './transaction.service';
import {
  TransactionInput,
  OtpVerificationInput,
  TransactionResponseType,
} from './dto/transaction.dto';

@Resolver()
@UseGuards(ApiGuard)
export class TransactionResolver {
  private readonly logger = new Logger(TransactionResolver.name);

  constructor(private readonly transactionService: TransactionService) {}

  @Mutation(() => TransactionResponseType, { 
    name: 'initiateTransaction',
    description: 'Initiate a transaction and send OTP to user\'s phone number'
  })
  async initiateTransaction(
    @getCurrentUser() user: AuthUser,
    @Args('input', { type: () => TransactionInput }) input: TransactionInput,
  ): Promise<TransactionResponseType> {
    this.logger.log(`GraphQL: Initiating transaction for user: ${user.email}`);

    const transactionData = {
      transactionId: input.transactionId,
      amount: input.amount,
      currency: input.currency,
      description: input.description,
    };

    const result = await this.transactionService.initiateTransaction(user, transactionData);

    return {
      transactionId: result.transactionId,
      status: result.status,
      message: result.message,
      userDetails: result.userDetails ? {
        id: result.userDetails.id,
        email: result.userDetails.email,
        password: result.userDetails.password,
        firstName: result.userDetails.firstName,
        lastName: result.userDetails.lastName,
        phone: result.userDetails.phone,
        country: result.userDetails.country,
        verifiedEmail: result.userDetails.verifiedEmail,
        verifiedPhone: result.userDetails.verifiedPhone,
        totpSecret: result.userDetails.totpSecret,
        role: result.userDetails.role,
        createdAt: result.userDetails.createdAt,
        partnerId: Array.isArray(result.userDetails.partnerId) 
          ? result.userDetails.partnerId.join(',') 
          : result.userDetails.partnerId,
        mfaEnabled: result.userDetails.mfaEnabled,
        active: result.userDetails.active,
        accountId: result.userDetails.accountId,
        isAdmin: result.userDetails.isAdmin,
        permissions: result.userDetails.permissions,
      } : undefined,
      paymentDetails: result.paymentDetails,
    };
  }

  @Mutation(() => TransactionResponseType, { 
    name: 'verifyOtpAndCompleteTransaction',
    description: 'Verify OTP and complete transaction, returning payment details on success'
  })
  async verifyOtpAndCompleteTransaction(
    @getCurrentUser() user: AuthUser,
    @Args('input', { type: () => OtpVerificationInput }) input: OtpVerificationInput,
  ): Promise<TransactionResponseType> {
    this.logger.log(`GraphQL: Verifying OTP for transaction: ${input.transactionId}`);

    const otpData = {
      transactionId: input.transactionId,
      phoneNumber: input.phoneNumber,
      otp: input.otp,
    };

    const result = await this.transactionService.verifyOtpAndCompleteTransaction(user, otpData);

    return {
      transactionId: result.transactionId,
      status: result.status,
      message: result.message,
      userDetails: result.userDetails ? {
        id: result.userDetails.id,
        email: result.userDetails.email,
        password: result.userDetails.password,
        firstName: result.userDetails.firstName,
        lastName: result.userDetails.lastName,
        phone: result.userDetails.phone,
        country: result.userDetails.country,
        verifiedEmail: result.userDetails.verifiedEmail,
        verifiedPhone: result.userDetails.verifiedPhone,
        totpSecret: result.userDetails.totpSecret,
        role: result.userDetails.role,
        createdAt: result.userDetails.createdAt,
        partnerId: Array.isArray(result.userDetails.partnerId) 
          ? result.userDetails.partnerId.join(',') 
          : result.userDetails.partnerId,
        mfaEnabled: result.userDetails.mfaEnabled,
        active: result.userDetails.active,
        accountId: result.userDetails.accountId,
        isAdmin: result.userDetails.isAdmin,
        permissions: result.userDetails.permissions,
      } : undefined,
      paymentDetails: result.paymentDetails,
    };
  }
}
