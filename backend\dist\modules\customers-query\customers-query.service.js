"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CustomersQueryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersQueryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let CustomersQueryService = CustomersQueryService_1 = class CustomersQueryService {
    prisma;
    logger = new common_1.Logger(CustomersQueryService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findMany(filter, pagination) {
        this.logger.log(`Finding customers with filter: ${JSON.stringify(filter)}`);
        const where = {
            deletedAt: null,
        };
        if (filter?.search) {
            where.OR = [
                { firstName: { contains: filter.search, mode: 'insensitive' } },
                { lastName: { contains: filter.search, mode: 'insensitive' } },
                { email: { contains: filter.search, mode: 'insensitive' } },
                { companyName: { contains: filter.search, mode: 'insensitive' } },
                { phone: { contains: filter.search, mode: 'insensitive' } },
                { taxId: { contains: filter.search, mode: 'insensitive' } },
            ];
        }
        if (filter?.status) {
            where.status = filter.status;
        }
        if (filter?.type) {
            where.type = filter.type;
        }
        if (filter?.isEmailVerified !== undefined) {
            where.isEmailVerified = filter.isEmailVerified;
        }
        if (filter?.isPhoneVerified !== undefined) {
            where.isPhoneVerified = filter.isPhoneVerified;
        }
        if (filter?.isKycVerified !== undefined) {
            where.isKycVerified = filter.isKycVerified;
        }
        if (filter?.tags && filter.tags.length > 0) {
            where.tags = {
                hasSome: filter.tags,
            };
        }
        if (filter?.companyName) {
            where.companyName = { contains: filter.companyName, mode: 'insensitive' };
        }
        if (filter?.country) {
            where.addresses = {
                some: {
                    country: { contains: filter.country, mode: 'insensitive' },
                },
            };
        }
        if (filter?.createdAfter || filter?.createdBefore) {
            where.createdAt = {};
            if (filter?.createdAfter) {
                where.createdAt.gte = filter.createdAfter;
            }
            if (filter?.createdBefore) {
                where.createdAt.lte = filter.createdBefore;
            }
        }
        if (filter?.lastLoginAfter || filter?.lastLoginBefore) {
            where.lastLoginAt = {};
            if (filter?.lastLoginAfter) {
                where.lastLoginAt.gte = filter.lastLoginAfter;
            }
            if (filter?.lastLoginBefore) {
                where.lastLoginAt.lte = filter.lastLoginBefore;
            }
        }
        const customers = await this.prisma.customer.findMany({
            where,
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
            skip: pagination?.skip,
            take: pagination?.take,
            orderBy: pagination?.orderBy || { createdAt: 'desc' },
        });
        return customers;
    }
    async findById(id) {
        this.logger.log(`Finding customer by ID: ${id}`);
        const customer = await this.prisma.customer.findFirst({
            where: {
                id,
                deletedAt: null,
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Customer with ID ${id} not found`);
        }
        return customer;
    }
    async findByEmail(email) {
        this.logger.log(`Finding customer by email: ${email}`);
        const customer = await this.prisma.customer.findFirst({
            where: {
                email,
                deletedAt: null,
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
        });
        if (!customer) {
            return null;
        }
        return customer;
    }
    async count(filter) {
        this.logger.log(`Counting customers with filter: ${JSON.stringify(filter)}`);
        const where = {
            deletedAt: null,
        };
        if (filter?.search) {
            where.OR = [
                { firstName: { contains: filter.search, mode: 'insensitive' } },
                { lastName: { contains: filter.search, mode: 'insensitive' } },
                { email: { contains: filter.search, mode: 'insensitive' } },
                { companyName: { contains: filter.search, mode: 'insensitive' } },
                { phone: { contains: filter.search, mode: 'insensitive' } },
                { taxId: { contains: filter.search, mode: 'insensitive' } },
            ];
        }
        if (filter?.status) {
            where.status = filter.status;
        }
        if (filter?.type) {
            where.type = filter.type;
        }
        if (filter?.isEmailVerified !== undefined) {
            where.isEmailVerified = filter.isEmailVerified;
        }
        if (filter?.isPhoneVerified !== undefined) {
            where.isPhoneVerified = filter.isPhoneVerified;
        }
        if (filter?.isKycVerified !== undefined) {
            where.isKycVerified = filter.isKycVerified;
        }
        if (filter?.tags && filter.tags.length > 0) {
            where.tags = {
                hasSome: filter.tags,
            };
        }
        if (filter?.companyName) {
            where.companyName = { contains: filter.companyName, mode: 'insensitive' };
        }
        if (filter?.country) {
            where.addresses = {
                some: {
                    country: { contains: filter.country, mode: 'insensitive' },
                },
            };
        }
        if (filter?.createdAfter || filter?.createdBefore) {
            where.createdAt = {};
            if (filter?.createdAfter) {
                where.createdAt.gte = filter.createdAfter;
            }
            if (filter?.createdBefore) {
                where.createdAt.lte = filter.createdBefore;
            }
        }
        if (filter?.lastLoginAfter || filter?.lastLoginBefore) {
            where.lastLoginAt = {};
            if (filter?.lastLoginAfter) {
                where.lastLoginAt.gte = filter.lastLoginAfter;
            }
            if (filter?.lastLoginBefore) {
                where.lastLoginAt.lte = filter.lastLoginBefore;
            }
        }
        return this.prisma.customer.count({ where });
    }
    async getStatistics() {
        this.logger.log(`Getting customer statistics`);
        const baseWhere = {
            deletedAt: null,
        };
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const [total, active, inactive, suspended, pendingVerification, blocked, emailVerified, phoneVerified, kycVerified, individual, business, enterprise, recentlyCreated, recentlyActive,] = await Promise.all([
            this.prisma.customer.count({ where: baseWhere }),
            this.prisma.customer.count({ where: { ...baseWhere, status: 'ACTIVE' } }),
            this.prisma.customer.count({ where: { ...baseWhere, status: 'INACTIVE' } }),
            this.prisma.customer.count({ where: { ...baseWhere, status: 'SUSPENDED' } }),
            this.prisma.customer.count({ where: { ...baseWhere, status: 'PENDING_VERIFICATION' } }),
            this.prisma.customer.count({ where: { ...baseWhere, status: 'BLOCKED' } }),
            this.prisma.customer.count({ where: { ...baseWhere, isEmailVerified: true } }),
            this.prisma.customer.count({ where: { ...baseWhere, isPhoneVerified: true } }),
            this.prisma.customer.count({ where: { ...baseWhere, isKycVerified: true } }),
            this.prisma.customer.count({ where: { ...baseWhere, type: 'INDIVIDUAL' } }),
            this.prisma.customer.count({ where: { ...baseWhere, type: 'BUSINESS' } }),
            this.prisma.customer.count({ where: { ...baseWhere, type: 'ENTERPRISE' } }),
            this.prisma.customer.count({
                where: { ...baseWhere, createdAt: { gte: thirtyDaysAgo } },
            }),
            this.prisma.customer.count({
                where: { ...baseWhere, lastLoginAt: { gte: thirtyDaysAgo } },
            }),
        ]);
        return {
            total,
            active,
            inactive,
            suspended,
            pendingVerification,
            blocked,
            emailVerified,
            phoneVerified,
            kycVerified,
            individual,
            business,
            enterprise,
            recentlyCreated,
            recentlyActive,
        };
    }
};
exports.CustomersQueryService = CustomersQueryService;
exports.CustomersQueryService = CustomersQueryService = CustomersQueryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CustomersQueryService);
//# sourceMappingURL=customers-query.service.js.map