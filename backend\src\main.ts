// src/main.ts
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { ConfigService } from './config/config.service';


async function bootstrap() {
  const isDevOrTest = process.env.NODE_ENV === "development" || process.env.NODE_ENV === "test";
  
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({ logger: true })
  );
  
  // Initialize configuration service
  const configService = app.get(ConfigService);
  
  // Register Fastify plugins with environment-aware CORS
  const localDomains = configService.getLocalDomains();
  const allowedOrigins = isDevOrTest && !configService.shouldBlockLocalhost()
    ? [
        'http://localhost:3000',  // Customer Embed
        'http://localhost:3001',  // Customer Admin
        'http://localhost:3002',  // Additional dev port
        'http://localhost:3003',  // Additional dev port
        'http://localhost:3004',  // Additional dev port
        'http://localhost:3005',  // Additional dev port
        'http://localhost:3060',  // Backend API
        'http://127.0.0.1:3000',  // Alternative localhost
        'http://127.0.0.1:3001',
        'http://127.0.0.1:3002',
        'http://127.0.0.1:3003',
        'http://127.0.0.1:3004',
        'http://127.0.0.1:3005',
        'http://127.0.0.1:3060',
        localDomains.backend,     // Local backend domain
        localDomains.frontend,    // Local frontend domain
        localDomains.admin,       // Local admin domain
        ...configService.getAllowedOrigins() // Include configured origins
      ]
    : configService.getAllowedOrigins();

  await app.register(require("@fastify/cors"), {
    origin: allowedOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
  });

  // Register cookie plugin for cookie parsing
  await app.register(require('@fastify/cookie'), {
    secret: process.env.COOKIE_SECRET || 'your-secret-key-here',
  });

  // Register multipart plugin for file uploads
  await app.register(require('@fastify/multipart'), {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  });
  
  // Enable validation pipes
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));
  
  // Remove global prefix - endpoints will be available directly without api/v1
  // const apiPrefix = configService.get('API_PREFIX', 'api');
  // app.setGlobalPrefix(apiPrefix);
  
  // Configure Swagger for Fastify
  const config = new DocumentBuilder()
    .setTitle('Customer Microservice API')
    .setDescription('Customer Management API Documentation')
    .setVersion('1.0')
    .addTag('customers-query', 'Customer search, filtering, and read operations')
    .addTag('customers-management', 'Customer create, update, and delete operations')
    .addTag('customers-verification', 'Customer email, phone, and KYC verification')
    .addTag('customers-admin', 'Administrative customer operations')
    .addTag('Authentication', 'Authentication and user management')
    .addTag('transactions', 'Transaction processing and OTP verification')
    .addTag('health', 'Health check endpoints')
    .addTag('webhooks', 'Generic webhook endpoints')
    .addBearerAuth()
    .addCookieAuth('access_token', {
      type: 'apiKey',
      in: 'cookie',
      name: 'access_token',
      description: 'Access token stored in cookie'
    })
    .addCookieAuth('refresh_token', {
      type: 'apiKey',
      in: 'cookie',
      name: 'refresh_token',
      description: 'Refresh token stored in cookie'
    })
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(`/api`, app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });
  
  const port = configService.getNumber("PORT", 3001);
  const listeningPort = process.env.LISTENING_PORT ? parseInt(process.env.LISTENING_PORT, 10) : 0;
  const bindAddress = isDevOrTest ? process.env.SOURCE_IP || "127.0.0.1" : "0.0.0.0";

  await app.listen(listeningPort || port, bindAddress);
  
  // Log information about the running application
  const baseUrl = `http://localhost:${listeningPort || port}`;
  const apiDocsUrl = `${baseUrl}/api`;
  const graphqlUrl = `${baseUrl}/graphql`;

  // Used only for development purposes
  /* eslint-disable no-console */
  console.log(`Application is running on: ${baseUrl}`);
  console.log(`Swagger documentation available at: ${apiDocsUrl}`);
  console.log(`GraphQL playground available at: ${graphqlUrl}`);
  /* eslint-enable no-console */
}

// Using void to mark that we're explicitly ignoring the promise
void bootstrap();
