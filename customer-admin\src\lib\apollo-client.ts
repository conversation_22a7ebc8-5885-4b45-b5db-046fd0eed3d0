import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Customer microservice GraphQL endpoint - fallback for build time
const CUSTOMER_GRAPHQL_URL = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL || 'https://ng-customer-dev.dev1.ngnair.com/graphql';

// Only validate in browser environment
if (typeof window !== 'undefined' && !process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL) {
  console.error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
}

// Create HTTP link
const httpLink = createHttpLink({
  uri: CUSTOMER_GRAPHQL_URL,
  credentials: 'include', // Include cookies for authentication
});

// Auth link for GraphQL requests - cookies are automatically included
const authLink = setContext((_, { headers }) => {
  return {
    headers: {
      ...headers,
      'Content-Type': 'application/json',
    }
  }
});

// Create Apollo Client
export const apolloClient = new ApolloClient({
  link: from([authLink, httpLink]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
  },
});
