"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const dotenv = require("dotenv");
const fs = require("fs");
const common_1 = require("@nestjs/common");
let ConfigService = ConfigService_1 = class ConfigService {
    envConfig;
    logger = new common_1.Logger(ConfigService_1.name);
    constructor() {
        const environment = process.env.NODE_ENV || "development";
        this.logger.log(`Loading configuration for environment: ${environment}`);
        try {
            const envFilePath = `.env.${environment}`;
            const fallbackEnvFilePath = ".env";
            if (fs.existsSync(envFilePath)) {
                this.logger.log(`Loading configuration from ${envFilePath}`);
                this.envConfig = dotenv.parse(fs.readFileSync(envFilePath));
            }
            else if (fs.existsSync(fallbackEnvFilePath)) {
                this.logger.log(`Loading configuration from ${fallbackEnvFilePath}`);
                this.envConfig = dotenv.parse(fs.readFileSync(fallbackEnvFilePath));
            }
            else {
                this.logger.warn("No .env file found. Using process.env only.");
                this.envConfig = {};
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Unknown error";
            this.logger.error(`Error loading configuration: ${errorMessage}`);
            this.envConfig = {};
        }
    }
    get(key, defaultValue) {
        const value = process.env[key] || this.envConfig[key] || defaultValue;
        return value;
    }
    getNumber(key, defaultValue = 0) {
        const value = this.get(key);
        if (value === undefined || value === "") {
            return defaultValue;
        }
        return Number(value);
    }
    getBoolean(key, defaultValue = false) {
        const value = this.get(key);
        if (value === undefined || value === "") {
            return defaultValue;
        }
        return String(value).toLowerCase() === "true";
    }
    getAllowedOrigins() {
        const origins = [];
        const originKeys = [
            'ALLOWED_ORIGIN_1', 'ALLOWED_ORIGIN_2', 'ALLOWED_ORIGIN_3',
            'ALLOWED_ORIGIN_4', 'ALLOWED_ORIGIN_5', 'ALLOWED_ORIGIN_6'
        ];
        for (const originKey of originKeys) {
            const origin = process.env[originKey] || this.envConfig[originKey];
            if (origin && typeof origin === 'string' && origin.trim() !== '') {
                origins.push(origin.trim());
            }
        }
        if (origins.length === 0) {
            const legacyOrigins = this.get("ALLOWED_ORIGINS", "http://localhost:3000");
            return String(legacyOrigins).split(",").map((origin) => origin.trim());
        }
        return origins;
    }
    shouldBlockLocalhost() {
        return this.getBoolean('CORS_BLOCK_LOCALHOST', false);
    }
    getLocalDomains() {
        return {
            backend: this.get('LOCAL_BACKEND_DOMAIN'),
            frontend: this.get('LOCAL_FRONTEND_DOMAIN'),
            admin: this.get('LOCAL_ADMIN_DOMAIN')
        };
    }
};
exports.ConfigService = ConfigService;
exports.ConfigService = ConfigService = ConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ConfigService);
//# sourceMappingURL=config.service.js.map