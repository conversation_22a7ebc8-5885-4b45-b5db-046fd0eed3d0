'use client';

import { useState } from 'react';
import { useRouter } from 'next/router';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CustomerGraphQLService } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { Loader2, Shield, Key } from 'lucide-react';

interface AdminLoginProps {
  onSuccess?: () => void;
}

export default function AdminLogin({ onSuccess }: AdminLoginProps) {
  const [accessToken, setAccessToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!accessToken.trim()) {
      toast.error('Please enter an access token');
      return;
    }

    setIsLoading(true);

    try {
      // Clean and validate the token
      const cleanToken = accessToken.trim().replace(/[^\x00-\x7F]/g, '');

      if (!cleanToken) {
        toast.error('Invalid access token format');
        return;
      }

      console.log('Attempting authentication with token length:', cleanToken.length);

      // Store the cleaned token first
      CustomerGraphQLService.storeAdminToken(cleanToken);

      // Try to authenticate with the GraphQL auth/me endpoint
      const user = await CustomerGraphQLService.getCurrentUser();
      
      // Check if user has admin role
      if (user.role !== 'global_admin' && user.role !== 'admin') {
        CustomerGraphQLService.clearAdminToken();
        toast.error('Access denied. Admin privileges required.');
        return;
      }

      toast.success(`Welcome back, ${user.username || user.email}!`);
      
      // Call success callback or redirect to dashboard
      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/dashboard');
      }
      
    } catch (error: any) {
      console.error('Authentication failed:', error);
      CustomerGraphQLService.clearAdminToken();
      
      // Handle different types of errors
      console.error('Full error details:', error);

      if (error.networkError) {
        console.error('Network error details:', error.networkError);
        toast.error('Network error. Please check if the customer service is running on localhost:3001.');
      } else if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        const graphQLError = error.graphQLErrors[0];
        console.error('GraphQL error details:', graphQLError);
        if (graphQLError.extensions?.code === 'UNAUTHENTICATED') {
          toast.error('Invalid access token. Please check your token and try again.');
        } else {
          toast.error(`Authentication failed: ${graphQLError.message}`);
        }
      } else if (error.message.includes('non ISO-8859-1')) {
        toast.error('Invalid token format. Please ensure your token contains only valid characters.');
      } else if (error.networkError && process.env.NODE_ENV === 'development') {
        toast.success('Using demo mode - customer service not available. You can explore the interface with mock data.');

        // In development mode, allow access with any token when service is not available
        if (onSuccess) {
          onSuccess();
        } else {
          router.push('/dashboard');
        }
        return;
      } else {
        toast.error('Authentication failed. Please check your access token and ensure the customer service is running.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    setIsTesting(true);

    const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
    if (!graphqlUrl) {
      toast.error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is not configured');
      setIsTesting(false);
      return;
    }

    try {
      const response = await fetch(graphqlUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: '{ __typename }',
        }),
      });

      if (response.ok) {
        toast.success('Successfully connected to customer service!');
      } else {
        toast.error(`Connection failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      toast.error('Failed to connect to customer service. Please check if it\'s running on localhost:3060.');
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <Shield className="h-8 w-8 text-primary" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">Admin Login</CardTitle>
          <CardDescription>
            Enter your access token to access the customer management system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="accessToken">Access Token</Label>
              <div className="relative">
                <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="accessToken"
                  type="password"
                  placeholder="Enter your access token"
                  value={accessToken}
                  onChange={(e) => setAccessToken(e.target.value)}
                  className="pl-10"
                  disabled={isLoading}
                  required
                />
              </div>
            </div>
            
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isLoading || !accessToken.trim()}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Authenticating...
                </>
              ) : (
                'Sign In'
              )}
            </Button>
          </form>
          
          <div className="mt-4">
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={testConnection}
              disabled={isTesting}
            >
              {isTesting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing Connection...
                </>
              ) : (
                'Test Connection to Customer Service'
              )}
            </Button>
          </div>

          <div className="mt-6 text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              Need an access token? Contact your system administrator.
            </p>
            <p className="text-xs text-muted-foreground">
              Customer Service: {process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL || 'http://localhost:3060/graphql'}
            </p>
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <p className="text-xs text-blue-700 dark:text-blue-300 font-medium mb-2">
                💡 For testing without customer service:
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                • Use "Test Connection" to check service status<br/>
                • Enter any token to access demo mode if service is offline<br/>
                • Demo mode includes mock customer data for exploration
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
