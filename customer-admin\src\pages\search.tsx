import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import { withAdminAuth } from '@/components/admin/withAdminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CustomerGraphQLService, Customer } from '@/services/customer-graphql.service';
import { toast } from 'sonner';
import { 
  Search,
  Filter,
  X,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Building,
  CheckCircle,
  XCircle,
  Eye,
  Download,
  RefreshCw
} from 'lucide-react';
import { format } from 'date-fns';

interface SearchFilters {
  query: string;
  status: string;
  emailVerified: string;
  phoneVerified: string;
  kycVerified: string;
  dateFrom: string;
  dateTo: string;
  city: string;
  state: string;
  country: string;
}

function CustomerSearchPage() {
  const router = useRouter();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const limit = 20;

  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    status: 'all',
    emailVerified: 'all',
    phoneVerified: 'all',
    kycVerified: 'all',
    dateFrom: '',
    dateTo: '',
    city: '',
    state: '',
    country: '',
  });

  useEffect(() => {
    // Load initial data or search results from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const query = urlParams.get('q');
    if (query) {
      setFilters(prev => ({ ...prev, query }));
      performSearch({ ...filters, query });
    }
  }, []);

  const performSearch = async (searchFilters = filters) => {
    try {
      setIsLoading(true);
      
      // Build search query
      let searchQuery = searchFilters.query;
      
      // Add filters to search query (this would be handled by the backend in a real implementation)
      const filterParts = [];
      if (searchFilters.status !== 'all') filterParts.push(`status:${searchFilters.status}`);
      if (searchFilters.emailVerified !== 'all') filterParts.push(`emailVerified:${searchFilters.emailVerified}`);
      if (searchFilters.phoneVerified !== 'all') filterParts.push(`phoneVerified:${searchFilters.phoneVerified}`);
      if (searchFilters.kycVerified !== 'all') filterParts.push(`kycVerified:${searchFilters.kycVerified}`);
      
      if (filterParts.length > 0) {
        searchQuery = searchQuery ? `${searchQuery} ${filterParts.join(' ')}` : filterParts.join(' ');
      }
      
      const data = await CustomerGraphQLService.getCustomers(
        currentPage,
        limit,
        searchQuery || undefined
      );
      
      // Apply client-side filtering for demo purposes
      let filteredCustomers = data.data;
      
      if (searchFilters.status !== 'all') {
        filteredCustomers = filteredCustomers.filter(c => c.status === searchFilters.status);
      }
      
      if (searchFilters.emailVerified !== 'all') {
        const verified = searchFilters.emailVerified === 'true';
        filteredCustomers = filteredCustomers.filter(c => c.emailVerified === verified);
      }
      
      if (searchFilters.phoneVerified !== 'all') {
        const verified = searchFilters.phoneVerified === 'true';
        filteredCustomers = filteredCustomers.filter(c => c.phoneVerified === verified);
      }
      
      if (searchFilters.kycVerified !== 'all') {
        const verified = searchFilters.kycVerified === 'true';
        filteredCustomers = filteredCustomers.filter(c => c.kycVerified === verified);
      }
      
      setCustomers(filteredCustomers);
      setTotalResults(filteredCustomers.length);
      
      // Update URL with search params
      const params = new URLSearchParams();
      if (searchFilters.query) params.set('q', searchFilters.query);
      if (searchFilters.status !== 'all') params.set('status', searchFilters.status);
      
      const newUrl = params.toString() ? `${window.location.pathname}?${params.toString()}` : window.location.pathname;
      window.history.replaceState({}, '', newUrl);
      
    } catch (error) {
      console.error('Search failed:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      status: 'all',
      emailVerified: 'all',
      phoneVerified: 'all',
      kycVerified: 'all',
      dateFrom: '',
      dateTo: '',
      city: '',
      state: '',
      country: '',
    });
    setCustomers([]);
    setTotalResults(0);
  };

  const exportResults = () => {
    // In a real implementation, this would generate and download a CSV/Excel file
    const csvContent = [
      ['ID', 'Name', 'Email', 'Phone', 'Status', 'Email Verified', 'Phone Verified', 'KYC Verified', 'Created'],
      ...customers.map(customer => [
        customer.id,
        `${customer.firstName} ${customer.lastName}`,
        customer.email,
        customer.phone || '',
        customer.status,
        customer.emailVerified ? 'Yes' : 'No',
        customer.phoneVerified ? 'Yes' : 'No',
        customer.kycVerified ? 'Yes' : 'No',
        format(new Date(customer.createdAt), 'yyyy-MM-dd'),
      ])
    ].map(row => row.join(',')).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `customer-search-results-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast.success('Search results exported successfully');
  };

  return (
    <AdminLayout title="Customer Search">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Advanced Customer Search</h2>
            <p className="text-muted-foreground">
              Search and filter customers with advanced criteria
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => performSearch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            {customers.length > 0 && (
              <Button variant="outline" size="sm" onClick={exportResults}>
                <Download className="h-4 w-4 mr-2" />
                Export Results
              </Button>
            )}
          </div>
        </div>

        {/* Search Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Search Criteria</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                <Filter className="h-4 w-4 mr-2" />
                {showAdvanced ? 'Hide' : 'Show'} Advanced Filters
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Basic Search */}
            <div className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="query">Search Query</Label>
                <Input
                  id="query"
                  placeholder="Search by name, email, phone, or ID..."
                  value={filters.query}
                  onChange={(e) => handleFilterChange('query', e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && performSearch()}
                />
              </div>
              <div className="flex items-end space-x-2">
                <Button onClick={() => performSearch()} disabled={isLoading}>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
                <Button variant="outline" onClick={clearFilters}>
                  <X className="h-4 w-4 mr-2" />
                  Clear
                </Button>
              </div>
            </div>

            {/* Advanced Filters */}
            {showAdvanced && (
              <div className="space-y-4 pt-4 border-t">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      value={filters.status}
                      onChange={(e) => handleFilterChange('status', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md text-sm"
                    >
                      <option value="all">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="suspended">Suspended</option>
                      <option value="pending">Pending</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="emailVerified">Email Verified</Label>
                    <select
                      id="emailVerified"
                      value={filters.emailVerified}
                      onChange={(e) => handleFilterChange('emailVerified', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md text-sm"
                    >
                      <option value="all">All</option>
                      <option value="true">Verified</option>
                      <option value="false">Not Verified</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="phoneVerified">Phone Verified</Label>
                    <select
                      id="phoneVerified"
                      value={filters.phoneVerified}
                      onChange={(e) => handleFilterChange('phoneVerified', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md text-sm"
                    >
                      <option value="all">All</option>
                      <option value="true">Verified</option>
                      <option value="false">Not Verified</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="kycVerified">KYC Verified</Label>
                    <select
                      id="kycVerified"
                      value={filters.kycVerified}
                      onChange={(e) => handleFilterChange('kycVerified', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md text-sm"
                    >
                      <option value="all">All</option>
                      <option value="true">Verified</option>
                      <option value="false">Not Verified</option>
                    </select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                  <div>
                    <Label htmlFor="dateFrom">Created From</Label>
                    <Input
                      id="dateFrom"
                      type="date"
                      value={filters.dateFrom}
                      onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="dateTo">Created To</Label>
                    <Input
                      id="dateTo"
                      type="date"
                      value={filters.dateTo}
                      onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      placeholder="City"
                      value={filters.city}
                      onChange={(e) => handleFilterChange('city', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      placeholder="State"
                      value={filters.state}
                      onChange={(e) => handleFilterChange('state', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="country">Country</Label>
                    <Input
                      id="country"
                      placeholder="Country"
                      value={filters.country}
                      onChange={(e) => handleFilterChange('country', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Search Results */}
        <Card>
          <CardHeader>
            <CardTitle>Search Results ({totalResults})</CardTitle>
            <CardDescription>
              {isLoading ? 'Searching...' : `Found ${totalResults} customers matching your criteria`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Search className="h-6 w-6 animate-pulse mr-2" />
                <span>Searching customers...</span>
              </div>
            ) : customers.length === 0 ? (
              <div className="text-center py-8">
                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-lg font-medium">No customers found</p>
                <p className="text-muted-foreground">Try adjusting your search criteria</p>
              </div>
            ) : (
              <div className="space-y-4">
                {customers.map((customer) => (
                  <div key={customer.id} className="border rounded-lg p-4 hover:bg-accent/50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">
                            {customer.firstName} {customer.lastName}
                          </h3>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            customer.status === 'active' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {customer.status}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Mail className="h-4 w-4" />
                            <span>{customer.email}</span>
                            {customer.emailVerified ? (
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            ) : (
                              <XCircle className="h-3 w-3 text-red-600" />
                            )}
                          </div>
                          
                          {customer.phone && (
                            <div className="flex items-center space-x-1">
                              <Phone className="h-4 w-4" />
                              <span>{customer.phone}</span>
                              {customer.phoneVerified ? (
                                <CheckCircle className="h-3 w-3 text-green-600" />
                              ) : (
                                <XCircle className="h-3 w-3 text-red-600" />
                              )}
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{format(new Date(customer.createdAt), 'MMM dd, yyyy')}</span>
                          </div>
                        </div>
                        
                        <p className="text-xs text-muted-foreground">ID: {customer.id}</p>
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/customers/${customer.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default withAdminAuth(CustomerSearchPage);
