services:
  api:
    build: .
    container_name: customer-server
    ports:
      - '3001:3001'
    env_file:
      - .env
    depends_on:
      - db
    environment:
      - DATABASE_URL=**************************************/postgres

  db:
    image: postgres:15-alpine
    container_name: customer-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - '5432:5432'
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./z-init.d:/docker-entrypoint-initdb.d

volumes:
  pgdata:
