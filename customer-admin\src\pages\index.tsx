import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../auth';
import { authService } from '../auth/services/authService';

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, login, error } = useAuth();

  useEffect(() => {
    console.log('🏠 HomePage useEffect - Auth State:', {
      isAuthenticated,
      isLoading,
      error,
      debugMode: process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE,
    });

    if (!isLoading) {
      if (isAuthenticated) {
        console.log('✅ User is authenticated, redirecting to dashboard');
        // Redirect to dashboard if authenticated
        router.push('/dashboard');
      } else {
        console.log('❌ User is not authenticated');
        // Check if debug mode is enabled
        const isDebugMode = process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE === 'true';
        console.log('🐛 Debug mode enabled:', isDebugMode);

        if (!isDebugMode) {
          console.log('🔄 Normal mode - redirecting to auth service');
          // Normal mode - redirect to auth service for login
          login();
        } else {
          console.log('🐛 Debug mode - showing debug interface');
        }
        // In debug mode, we'll show the debug interface below instead of auto-redirecting
      }
    }
  }, [isAuthenticated, isLoading, router, login]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Head>
          <title>Customer Management System</title>
          <meta name="description" content="Customer Management System Admin Panel" />
          <link rel="icon" href="/favicon.ico" />
        </Head>

        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Customer Management System</h1>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Show debug interface if not authenticated and debug mode is enabled
  if (!isAuthenticated && process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE === 'true') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Head>
          <title>Customer Management System - Debug</title>
          <meta name="description" content="Customer Management System Admin Panel" />
          <link rel="icon" href="/favicon.ico" />
        </Head>

        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Authentication Debug</h2>
            <p className="text-gray-600">Debug mode is enabled</p>
          </div>

          <div className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-red-800 mb-2">Authentication Status</h3>
              <p className="text-sm text-red-700">Not authenticated</p>
              {error && (
                <p className="text-sm text-red-700 mt-1">Error: {error}</p>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-blue-800 mb-2">Cookie Information</h3>
              <p className="text-sm text-blue-700">
                Access Token: {typeof window !== 'undefined' && document.cookie.includes('access_token') ? '✅ Present' : '❌ Missing'}
              </p>
              <p className="text-sm text-blue-700">
                Refresh Token: {typeof window !== 'undefined' && document.cookie.includes('refresh_token') ? '✅ Present' : '❌ Missing'}
              </p>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-yellow-800 mb-2">Debug Actions</h3>
              <div className="space-y-2">
                <button
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      console.log('Cookies:', document.cookie);
                      console.log('Auth Error:', error);
                      console.log('Environment:', {
                        authFrontendUrl: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL,
                        authJwksUrl: process.env.NEXT_PUBLIC_AUTH_JWKS_URL,
                        backendUrl: process.env.NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN,
                        debugMode: process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE,
                        cookieDomain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
                        allowedOrigins: process.env.NEXT_PUBLIC_ALLOWED_ORIGINS
                      });
                    }
                  }}
                  className="w-full text-left text-sm text-yellow-700 hover:text-yellow-900 underline"
                >
                  📋 Log debug info to console
                </button>
                <button
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      authService.testCookieConfiguration();
                    }
                  }}
                  className="w-full text-left text-sm text-yellow-700 hover:text-yellow-900 underline"
                >
                  🧪 Test cookie configuration
                </button>
                <button
                  onClick={() => login()}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                >
                  🔐 Go to Auth Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default fallback (should not normally be reached)
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Head>
        <title>Customer Management System</title>
        <meta name="description" content="Customer Management System Admin Panel" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Customer Management System</h1>
        <p className="text-gray-600">Redirecting...</p>
      </div>
    </div>
  );
}