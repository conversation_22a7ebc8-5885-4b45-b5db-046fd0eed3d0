import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '../../config/config.service';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';
import { User, JWTPayload, AuthConfig, DecryptedTokens } from './types/auth.types';
import { firstValueFrom } from 'rxjs';

// JWKS client for JWT verification - temporarily disabled for testing
// const jwksClient = require('jwks-client');

@Injectable()
export class AuthSharedService {
  private readonly logger = new Logger(AuthSharedService.name);
  private readonly config: AuthConfig;
  // private jwksClientInstance: any; // Temporarily disabled

  constructor(
    private configService: ConfigService,
    private httpService: HttpService
  ) {
    this.config = {
      authJwksUrl: this.configService.get('AUTH_JWKS_URL') || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
      encryptionKey: this.configService.get('ACCESS_TOKEN_ENCRYPTION_KEY') || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
      cookieNames: {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      },
    };

    // Initialize JWKS client for JWT verification - temporarily disabled
    // this.jwksClientInstance = jwksClient({
    //   jwksUri: this.config.authJwksUrl,
    //   requestHeaders: {},
    //   timeout: 30000,
    // });
  }

  /**
   * Decrypt cookies using Rails-compatible AES-256-GCM encryption
   */
  private decryptCookie(encryptedValue: string): string {
    try {
      this.logger.log(`Attempting to decrypt token: ${encryptedValue.substring(0, 50)}...`);

      // First, try to decode as URL-safe base64 (the token might be base64 encoded)
      let decodedValue: string;
      try {
        // Convert URL-safe base64 to standard base64
        const standardBase64 = encryptedValue.replace(/-/g, '+').replace(/_/g, '/');
        // Add padding if needed
        const paddedBase64 = standardBase64 + '='.repeat((4 - standardBase64.length % 4) % 4);
        decodedValue = Buffer.from(paddedBase64, 'base64').toString('utf8');
        this.logger.log(`Base64 decoded successfully, length: ${decodedValue.length}`);
      } catch (base64Error) {
        // If base64 decoding fails, try URL decoding
        this.logger.log('Base64 decoding failed, trying URL decoding...');
        decodedValue = decodeURIComponent(encryptedValue);
        this.logger.log(`URL decoded length: ${decodedValue.length}`);
      }

      // Rails MessageEncryptor format: base64(encrypted_data)--base64(iv)--base64(auth_tag)
      const parts = decodedValue.split('--');
      this.logger.log(`Split into ${parts.length} parts`);

      if (parts.length !== 3) {
        this.logger.error(`Invalid encrypted cookie format. Expected 3 parts, got ${parts.length}`);
        this.logger.error(`First 100 chars of decoded value: ${decodedValue.substring(0, 100)}`);
        throw new Error('Invalid encrypted cookie format');
      }

      const encryptedData = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');
      const authTag = Buffer.from(parts[2], 'base64');

      this.logger.log(`Encrypted data length: ${encryptedData.length}, IV length: ${iv.length}, Auth tag length: ${authTag.length}`);

      // Convert hex key to buffer
      const key = Buffer.from(this.config.encryptionKey, 'hex');
      this.logger.log(`Key length: ${key.length} bytes`);

      // Create decipher for GCM mode
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      // Decrypt
      let decrypted = decipher.update(encryptedData, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      this.logger.log(`Decryption successful, result length: ${decrypted.length}`);

      // Remove quotes if present (Rails adds quotes around JSON strings)
      const result = decrypted.replace(/^"(.*)"$/, '$1');
      this.logger.log(`Final result length after quote removal: ${result.length}`);

      return result;
    } catch (error) {
      this.logger.error('Failed to decrypt cookie:', error);
      throw new UnauthorizedException('Invalid encrypted token');
    }
  }

  /**
   * Extract raw encrypted tokens from cookies (without decryption)
   */
  extractRawTokensFromCookies(cookies: Record<string, string>): { accessToken?: string; refreshToken?: string } {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    // URL decode the tokens since cookies are automatically URL-encoded by browsers
    const decodedAccessToken = encryptedAccessToken ? decodeURIComponent(encryptedAccessToken) : undefined;
    const decodedRefreshToken = encryptedRefreshToken ? decodeURIComponent(encryptedRefreshToken) : undefined;

    if (encryptedAccessToken && decodedAccessToken) {
      this.logger.log(`🔗 [AUTH SERVICE] URL-encoded token length: ${encryptedAccessToken.length} characters`);
      this.logger.log(`🔓 [AUTH SERVICE] URL-decoded token length: ${decodedAccessToken.length} characters`);
      this.logger.log(`🔓 [AUTH SERVICE] Decoded token preview: ${decodedAccessToken.substring(0, 50)}...`);
    }

    return {
      accessToken: decodedAccessToken,
      refreshToken: decodedRefreshToken
    };
  }

  /**
   * Extract and decrypt tokens from cookies
   */
  extractTokensFromCookies(cookies: Record<string, string>): DecryptedTokens {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    if (!encryptedAccessToken) {
      throw new UnauthorizedException('Missing access token');
    }

    if (!encryptedRefreshToken) {
      this.logger.warn('Refresh token not found, proceeding with access token only');
    }

    try {
      const accessToken = this.decryptCookie(encryptedAccessToken);
      const refreshToken = encryptedRefreshToken ? this.decryptCookie(encryptedRefreshToken) : undefined;

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to decrypt tokens:', error);
      throw new UnauthorizedException('Invalid authentication tokens');
    }
  }



  /**
   * Verify JWT token using JWKS - temporarily simplified for testing
   */
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      // Decode token header to get kid
      const decoded = jwt.decode(token, { complete: true });

      if (!decoded || !decoded.header) {
        throw new UnauthorizedException('Invalid token format');
      }

      // Temporarily skip JWKS verification - just decode the payload
      this.logger.warn('JWKS verification temporarily disabled - only decoding JWT payload');
      const payload = decoded.payload as JWTPayload;

      // Basic validation
      if (!payload.sub || !payload.email) {
        throw new UnauthorizedException('Invalid JWT payload - missing required fields');
      }

      return payload;
    } catch (error) {
      this.logger.error('Token verification failed:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Get user from JWT payload
   */
  getUserFromPayload(payload: JWTPayload): User {
    // Provide default role if not present in JWT
    const role = payload.role || 'admin'; // Default to 'admin' for admin panel access

    this.logger.log(`📋 [AUTH SERVICE] User role from JWT: ${payload.role || 'undefined'}, using: ${role}`);

    return {
      id: payload.sub,
      email: payload.email,
      username: payload.username || payload.email, // Use email as username if not provided
      firstName: payload.first_name || payload.firstName,
      lastName: payload.last_name || payload.lastName,
      role: role,
      permissions: payload.permissions || [],
      createdAt: new Date(payload.iat * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
      // Additional fields from external auth service
      accounts: payload.accounts,
      partners: payload.partners,
      active_accounts: payload.active_accounts,
      active_partners: payload.active_partners,
      first_name: payload.first_name,
      last_name: payload.last_name,
    };
  }

  /**
   * Authenticate user from cookies
   */
  async authenticateFromCookies(cookies: Record<string, string>): Promise<User> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract and decrypt tokens
      this.logger.log('🔓 [AUTH SERVICE] Extracting and decrypting tokens...');
      const { accessToken } = this.extractTokensFromCookies(cookies);
      this.logger.log(`🔑 [AUTH SERVICE] Access token extracted: ${accessToken ? 'YES' : 'NO'}`);

      // Verify access token
      this.logger.log('✅ [AUTH SERVICE] Verifying access token...');
      const payload = await this.verifyToken(accessToken);
      this.logger.log(`👤 [AUTH SERVICE] Token verified, user ID: ${payload.sub}`);

      // Return user info
      this.logger.log('📋 [AUTH SERVICE] Getting user info from payload...');
      const user = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] Authentication successful: ${user.email} (${user.role})`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Refresh authentication using refresh token
   */
  async refreshAuthentication(cookies: Record<string, string>): Promise<boolean> {
    try {
      // Extract and decrypt tokens
      const { refreshToken } = this.extractTokensFromCookies(cookies);

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not available');
      }

      // Verify refresh token
      await this.verifyToken(refreshToken);

      // In a real implementation, you would call the auth service to get new tokens
      // and set new cookies. For now, we just verify that the refresh token is valid
      return true;
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      return false;
    }
  }

  /**
   * Check if user has required permissions
   */
  hasPermissions(user: User, requiredPermissions: string[]): boolean {
    if (!user.permissions) return false;
    return requiredPermissions.every(permission => user.permissions!.includes(permission));
  }

  /**
   * Check if user has required role
   */
  hasRole(user: User, requiredRole: string): boolean {
    return user.role === requiredRole;
  }

  /**
   * Authenticate user from cookies by calling external auth service
   */
  async authenticateFromCookiesViaExternalService(cookies: Record<string, string>): Promise<User> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies via external service');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Customer-Service/1.0',
      };

      // Log complete request details for debugging
      this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
      this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
      this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
      this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
      this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);

      // Forward the encrypted access_token as a cookie to external service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] User authenticated via external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the first user from the response (assuming it's an array)
      const userData = Array.isArray(response.data) ? response.data[0] : response.data;
      return userData;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] External authentication failed: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Manually set access token cookie for debugging purposes
   */
  setAccessTokenCookie(response: any, token: string): boolean {
    try {
      this.logger.log(`🍪 [AUTH SERVICE] Setting access token cookie manually`);
      this.logger.log(`🔐 [AUTH SERVICE] Token length: ${token.length} characters`);
      this.logger.log(`🔐 [AUTH SERVICE] Token preview: ${token.substring(0, 50)}...`);

      // URL encode the token value
      const encodedToken = encodeURIComponent(token);
      this.logger.log(`🔗 [AUTH SERVICE] URL encoded token length: ${encodedToken.length} characters`);

      // Set cookie with specified parameters
      const cookieOptions = [
        `access_token=${encodedToken}`,
        'Domain=.dev1.ngnair.com',
        'Path=/',
        'Max-Age=900', // 15 minutes
        'SameSite=Lax'
      ].join('; ');

      response.setHeader('Set-Cookie', cookieOptions);
      this.logger.log(`✅ [AUTH SERVICE] Cookie set successfully: ${cookieOptions.substring(0, 100)}...`);

      return true;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to set cookie: ${error.message}`);
      return false;
    }
  }

  /**
   * Decrypt token for testing purposes (public method)
   */
  async decryptTokenForTesting(encryptedToken: string): Promise<string> {
    return this.decryptCookie(encryptedToken);
  }

  async encryptTokenForTesting(payload: string): Promise<string> {
    try {
      this.logger.log(`Encrypting test payload: ${payload.substring(0, 100)}...`);

      // Convert hex key to buffer
      const key = Buffer.from(this.config.encryptionKey, 'hex');

      // Generate random IV (12 bytes for GCM)
      const iv = crypto.randomBytes(12);

      // Create cipher for GCM mode
      const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);

      // Encrypt the payload
      let encrypted = cipher.update(payload, 'utf8');
      encrypted = Buffer.concat([encrypted, cipher.final()]);

      // Get the authentication tag
      const authTag = cipher.getAuthTag();

      // Create Rails MessageEncryptor format: base64(encrypted_data)--base64(iv)--base64(auth_tag)
      const encryptedData = encrypted.toString('base64');
      const ivBase64 = iv.toString('base64');
      const authTagBase64 = authTag.toString('base64');

      const railsFormat = `${encryptedData}--${ivBase64}--${authTagBase64}`;

      // Encode as URL-safe base64
      const urlSafeBase64 = Buffer.from(railsFormat, 'utf8').toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

      this.logger.log(`Encryption successful, result length: ${urlSafeBase64.length}`);

      return urlSafeBase64;
    } catch (error) {
      this.logger.error('Failed to encrypt payload:', error);
      throw new UnauthorizedException('Failed to encrypt payload');
    }
  }

  /**
   * Authenticate user from cookies using proper external auth service integration
   * This is the main method for the /auth/me endpoint
   */
  async authenticateUserFromCookies(cookies: Record<string, string>): Promise<User> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication for /auth/me endpoint');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract encrypted access token from cookies
      const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      this.logger.log(`🔑 [AUTH SERVICE] Found encrypted access token: ${encryptedAccessToken.substring(0, 50)}...`);

      // Step 1: Decrypt the access token
      this.logger.log('🔓 [AUTH SERVICE] Step 1: Decrypting access token...');
      const decryptedToken = this.decryptCookie(encryptedAccessToken);
      this.logger.log(`✅ [AUTH SERVICE] Token decrypted successfully, length: ${decryptedToken.length}`);

      // Step 2: Verify JWT using JWKS
      this.logger.log('🔍 [AUTH SERVICE] Step 2: Verifying JWT with JWKS...');
      const payload = await this.verifyToken(decryptedToken);
      this.logger.log(`✅ [AUTH SERVICE] JWT verified successfully for user: ${payload.sub}`);

      // Step 3: Extract user information from JWT payload
      this.logger.log('👤 [AUTH SERVICE] Step 3: Extracting user information...');
      const user = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] User authenticated successfully: ${user.email} (${user.role})`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Get user by ID from external auth service
   */
  async getUserById(userId: string, cookies: Record<string, string>): Promise<User> {
    this.logger.log(`🔍 [AUTH SERVICE] Getting user by ID: ${userId}`);
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users/${userId}`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Customer-Service/1.0',
      };

      // Log complete request details for debugging
      this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
      this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
      this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
      this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
      this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);

      // Forward the encrypted access_token as a cookie to external service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] User retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the user data from the response
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get user by ID from external service: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      if (error.response?.status === 404) {
        throw new UnauthorizedException('User not found');
      }
      throw new UnauthorizedException('Failed to retrieve user information');
    }
  }

  /**
   * Get all users from external auth service
   */
  async getAllUsers(cookies: Record<string, string>): Promise<User[]> {
    this.logger.log('🔍 [AUTH SERVICE] Getting all users from external service');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Customer-Service/1.0',
      };

      // Log complete request details for debugging
      this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
      this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
      this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
      this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
      this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);

      // Forward the encrypted access_token as a cookie to external service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] Users retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the users array from the response
      const usersData = Array.isArray(response.data) ? response.data : [response.data];
      return usersData;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get all users from external service: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      throw new UnauthorizedException('Failed to retrieve users information');
    }
  }
}
