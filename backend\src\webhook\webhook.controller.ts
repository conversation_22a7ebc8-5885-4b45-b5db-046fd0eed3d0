import { 
  <PERSON>, 
  Post, 
  Body, 
  Logger, 
  HttpStatus,
  HttpException,
  Get
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { WebhookService } from "./webhook.service";

@ApiTags("webhooks")
@Controller("webhooks")
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly webhookService: WebhookService) {}

  @Post()
  @ApiOperation({ summary: "Handle generic webhook" })
  @ApiBody({
    schema: {
      type: "object"
    }
  })
  @ApiResponse({
    status: 200,
    description: "Webhook processed successfully"
  })
  async handleWebhook(
    @Body() payload: Record<string, unknown>,
  ): Promise<Record<string, unknown>> {
    try {
      this.logger.log(`Received webhook: ${JSON.stringify(payload)}`);
      return await this.webhookService.processWebhook(payload);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error handling webhook: ${error.message}`, error.stack);
      } else {
        this.logger.error("Unknown error handling webhook");
      }
      throw new HttpException("Webhook processing failed", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post("elavon")
  @ApiOperation({ summary: "Handle Elavon payment webhook" })
  @ApiBody({
    description: "Elavon payment webhook payload",
    schema: {
      type: "object",
      properties: {
        reference_id: { type: "string" },
        currency: { type: "string" },
        amount: { type: "number" },
        provider_reference_id: { type: "string" },
        ssl_token: { type: "string" },
        transaction_id: { type: "string" },
        customer_name: { type: "string" },
        customer_id: { type: "string" },
        card_type: { type: "string" },
        status: { type: "string" },
        raw_response: { type: "object" }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: "Elavon webhook processed successfully"
  })
  async handleElavonWebhook(
    @Body() payload: Record<string, unknown>,
  ): Promise<Record<string, unknown>> {
    try {
      this.logger.log(`Received Elavon webhook: ${JSON.stringify(payload)}`);
      return await this.webhookService.processWebhook(payload);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error handling Elavon webhook: ${error.message}`, error.stack);
      } else {
        this.logger.error("Unknown error handling Elavon webhook");
      }
      throw new HttpException("Elavon webhook processing failed", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post("tsep")
  @ApiOperation({ summary: "Handle TSEP payment webhook" })
  @ApiBody({
    description: "TSEP payment webhook payload",
    schema: {
      type: "object",
      properties: {
        reference_id: { type: "string" },
        currency: { type: "string" },
        amount: { type: "number" },
        provider_reference_id: { type: "string" },
        tsep_token: { type: "string" },
        transaction_id: { type: "string" },
        customer_name: { type: "string" },
        customer_id: { type: "string" },
        card_type: { type: "string" },
        status: { type: "string" },
        response_code: { type: "string" },
        raw_response: { type: "object" }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: "TSEP webhook processed successfully"
  })
  async handleTSEPWebhook(
    @Body() payload: Record<string, unknown>,
  ): Promise<Record<string, unknown>> {
    try {
      this.logger.log(`Received TSEP webhook: ${JSON.stringify(payload)}`);
      return await this.webhookService.processWebhook(payload);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error handling TSEP webhook: ${error.message}`, error.stack);
      } else {
        this.logger.error("Unknown error handling TSEP webhook");
      }
      throw new HttpException("TSEP webhook processing failed", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get("health")
  @ApiOperation({ summary: "Health check for webhook endpoint" })
  @ApiResponse({
    status: 200,
    description: "Webhook service is healthy",
    schema: {
      properties: {
        status: { type: "string", example: "ok" }
      }
    }
  })
  healthCheck() {
    return { status: "ok" };
  }
}
