import { Module } from '@nestjs/common';
import { CustomersAdminService } from './customers-admin.service';
import { CustomersAdminController } from './customers-admin.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '../../config/config.module';
import { SharedGuardsModule } from '../shared/guards.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    SharedGuardsModule,
  ],
  controllers: [
    CustomersAdminController,
  ],
  providers: [
    CustomersAdminService,
  ],
  exports: [
    CustomersAdminService,
  ],
})
export class CustomersAdminModule {}
