import { User, AuthConfig } from '../types/auth.types';

export class AuthService {
  private config: AuthConfig;

  constructor(config: AuthConfig) {
    this.config = config;
  }

  /**
   * Check if user has valid authentication cookies
   */
  hasAuthCookies(): boolean {
    if (typeof document === 'undefined') return false;

    console.log('🍪 Checking cookies - Raw document.cookie:', document.cookie);
    console.log('🍪 Current domain:', window.location.hostname);
    console.log('🍪 Current origin:', window.location.origin);

    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, string>);

    console.log('🍪 Parsed cookies:', cookies);
    console.log('🍪 Looking for cookies:', {
      accessToken: this.config.cookieNames.accessToken,
      refreshToken: this.config.cookieNames.refreshToken,
    });

    const hasAccessToken = !!cookies[this.config.cookieNames.accessToken];
    const hasRefreshToken = !!cookies[this.config.cookieNames.refreshToken];

    console.log('🍪 Cookie status:', {
      hasAccessToken,
      hasRefreshToken,
      accessTokenValue: hasAccessToken ? '***PRESENT***' : 'MISSING',
      refreshTokenValue: hasRefreshToken ? '***PRESENT***' : 'MISSING',
    });

    // If cookies are not found locally, we'll let getCurrentUser() handle the backend check
    // This allows the backend to access cookies that might be set for different domains
    console.log('🍪 Local cookie check result:', hasAccessToken && hasRefreshToken);

    return hasAccessToken && hasRefreshToken;
  }

  /**
   * Test cookie functionality and domain configuration
   */
  testCookieConfiguration(): void {
    console.log('🧪 Testing Cookie Configuration...');

    // Test setting a test cookie
    const testCookieName = 'auth_test_cookie';
    const testCookieValue = 'test_value_' + Date.now();

    console.log('🧪 Setting test cookie:', testCookieName, '=', testCookieValue);
    document.cookie = `${testCookieName}=${testCookieValue}; path=/; domain=${window.location.hostname}`;

    // Try to read it back
    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, string>);

    const testCookieRead = cookies[testCookieName];
    console.log('🧪 Test cookie read back:', testCookieRead);

    if (testCookieRead === testCookieValue) {
      console.log('✅ Cookie functionality working correctly');
    } else {
      console.log('❌ Cookie functionality not working');
    }

    // Clean up test cookie
    document.cookie = `${testCookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`;

    // Test domain configuration
    console.log('🧪 Domain Configuration Test:');
    console.log('Current hostname:', window.location.hostname);
    console.log('Current origin:', window.location.origin);
    console.log('Configured cookie domain:', this.config.domains?.cookieDomain || 'Not configured');
    console.log('Allowed origins:', this.config.domains?.allowedOrigins || 'Not configured');

    // Check if current domain matches cookie domain
    const cookieDomain = this.config.domains?.cookieDomain;
    if (cookieDomain) {
      const currentHostname = window.location.hostname;
      const domainMatches = currentHostname.endsWith(cookieDomain.replace('.', ''));
      console.log('🧪 Domain match check:', domainMatches ? '✅ Match' : '❌ No match');
    }
  }

  /**
   * Redirect to external auth service for login
   */
  login(redirectUrl?: string): void {
    const currentUrl = redirectUrl || window.location.href;
    const callbackUrl = `${window.location.origin}/auth/callback`;
    const loginUrl = `${this.config.authFrontendUrl}/login?redirect=${encodeURIComponent(callbackUrl)}&return_to=${encodeURIComponent(currentUrl)}`;

    console.log('🔐 Redirecting to login:', {
      currentUrl,
      callbackUrl,
      loginUrl
    });

    window.location.href = loginUrl;
  }

  /**
   * Logout by calling backend logout endpoint and redirecting to auth service
   */
  async logout(): Promise<void> {
    try {
      console.log('🔐 Starting logout process...');

      // First, call backend logout endpoint to clear server-side cookies
      await this.logoutFromBackend();

      // Then clear local cookies as fallback
      this.clearLocalCookies();

      // Finally, redirect to auth service logout
      console.log('🔄 Redirecting to auth service logout...');
      const logoutUrl = `${this.config.authFrontendUrl}/logout`;
      window.location.href = logoutUrl;
    } catch (error) {
      console.error('❌ Logout error:', error);

      // Even if backend logout fails, still clear local cookies and redirect
      this.clearLocalCookies();
      const logoutUrl = `${this.config.authFrontendUrl}/logout`;
      window.location.href = logoutUrl;
    }
  }

  /**
   * Call backend logout endpoint to clear server-side cookies
   */
  private async logoutFromBackend(): Promise<void> {
    try {
      console.log('🔐 Calling backend logout endpoint...');

      const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
      if (!graphqlUrl) {
        throw new Error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }

      // Try GraphQL logout mutation first
      try {
        const { apolloClient } = await import('@/lib/apollo-client');
        const { gql } = await import('@apollo/client');

        const LOGOUT_MUTATION = gql`
          mutation Logout {
            logout
          }
        `;

        const { data } = await apolloClient.mutate({
          mutation: LOGOUT_MUTATION,
          errorPolicy: 'all',
        });

        if (data?.logout) {
          console.log('✅ Backend logout successful via GraphQL');
          return;
        }
      } catch (apolloError) {
        console.log('⚠️ GraphQL logout failed, trying REST API...');
      }

      // Fallback to REST API logout
      const apiUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL;
      if (apiUrl) {
        const response = await fetch(`${apiUrl}/auth/logout`, {
          method: 'POST',
          credentials: 'include', // Include cookies
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          console.log('✅ Backend logout successful via REST API');
          return;
        }
      }

      throw new Error('Both GraphQL and REST logout failed');
    } catch (error) {
      console.error('❌ Backend logout failed:', error);
      throw error;
    }
  }

  /**
   * Clear local cookies as fallback
   */
  private clearLocalCookies(): void {
    console.log('🧹 Clearing local cookies...');

    // Clear cookies with different domain configurations
    const domains = [
      '', // Current domain
      window.location.hostname,
      `.${window.location.hostname}`,
      '.dev1.ngnair.com', // Parent domain
    ];

    domains.forEach(domain => {
      const cookieOptions = domain ? `; domain=${domain}` : '';
      document.cookie = `${this.config.cookieNames.accessToken}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/${cookieOptions}`;
      document.cookie = `${this.config.cookieNames.refreshToken}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/${cookieOptions}`;
    });

    console.log('✅ Local cookies cleared');
  }

  /**
   * Get user info from backend using GraphQL (backend will decrypt and verify tokens)
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      console.log('🔍 Getting current user via GraphQL...');

      const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
      if (!graphqlUrl) {
        throw new Error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }

      console.log('🌐 GraphQL URL:', graphqlUrl);

      // Import Apollo client dynamically to avoid SSR issues
      const { apolloClient } = await import('@/lib/apollo-client');
      const { gql } = await import('@apollo/client');

      const ME_COOKIES_QUERY = gql`
        query MeCookies {
          meCookies {
            id
            email
            username
            firstName
            lastName
            role
            permissions
            createdAt
            updatedAt
          }
        }
      `;

      const { data } = await apolloClient.query({
        query: ME_COOKIES_QUERY,
        fetchPolicy: 'network-only', // Always fetch fresh data
        errorPolicy: 'all',
      });

      if (data?.meCookies) {
        console.log('✅ User authenticated via GraphQL:', data.meCookies.email);
        return data.meCookies;
      }

      console.log('❌ No user data returned from GraphQL');
      return null;
    } catch (error) {
      console.error('❌ Error getting current user via GraphQL:', error);
      return null;
    }
  }

  /**
   * Refresh authentication using GraphQL
   */
  async refreshAuth(): Promise<boolean> {
    try {
      console.log('🔄 Refreshing authentication via GraphQL...');

      const graphqlUrl = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;
      if (!graphqlUrl) {
        throw new Error('NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL environment variable is required');
      }

      console.log('🌐 GraphQL URL for refresh:', graphqlUrl);

      // Import Apollo client dynamically to avoid SSR issues
      const { apolloClient } = await import('@/lib/apollo-client');
      const { gql } = await import('@apollo/client');

      const REFRESH_AUTH_MUTATION = gql`
        mutation RefreshAuth {
          refreshAuth
        }
      `;

      const { data } = await apolloClient.mutate({
        mutation: REFRESH_AUTH_MUTATION,
        errorPolicy: 'all',
      });

      if (data?.refreshAuth) {
        console.log('✅ Authentication refreshed successfully via GraphQL');
        return true;
      }

      console.log('❌ Failed to refresh authentication via GraphQL');
      return false;
    } catch (error) {
      console.error('❌ Error refreshing authentication via GraphQL:', error);
      return false;
    }
  }
}

// Environment variables are configured via .env files

// Configuration - with fallbacks for build time
export const defaultAuthConfig: AuthConfig = {
  authFrontendUrl: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL || 'https://ng-auth-fe-dev.dev1.ngnair.com',
  authJwksUrl: process.env.NEXT_PUBLIC_AUTH_JWKS_URL || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
  encryptionKey: process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },
  domains: {
    cookieDomain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
    allowedOrigins: process.env.NEXT_PUBLIC_ALLOWED_ORIGINS?.split(','),
  },
};

// Auth configuration created from environment variables

// Validate required environment variables (only in browser, but don't throw errors)
if (typeof window !== 'undefined') {
  console.log('🔧 Environment Configuration:');
  console.log('Customer GraphQL URL:', process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL);
  console.log('Auth Frontend URL:', process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL);
  console.log('Auth JWKS URL:', process.env.NEXT_PUBLIC_AUTH_JWKS_URL);

  if (!process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL) {
    console.error('❌ NEXT_PUBLIC_AUTH_FRONTEND_URL is missing');
  }
  if (!process.env.NEXT_PUBLIC_AUTH_JWKS_URL) {
    console.error('❌ NEXT_PUBLIC_AUTH_JWKS_URL is missing');
  }
  if (!process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY) {
    console.error('❌ NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY is missing');
  }
  if (!process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL) {
    console.error('❌ NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL is missing');
  }

  if (process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL &&
      process.env.NEXT_PUBLIC_AUTH_JWKS_URL &&
      process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY &&
      process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL) {
    console.log('✅ All auth environment variables validated successfully');
  }
}

// Export singleton instance
export const authService = new AuthService(defaultAuthConfig);
