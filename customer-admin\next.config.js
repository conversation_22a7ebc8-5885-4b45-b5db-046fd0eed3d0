/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  env: {
    NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY: process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
    NEXT_PUBLIC_AUTH_FRONTEND_URL: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL || 'https://ng-auth-fe-dev.dev1.ngnair.com',
    NEXT_PUBLIC_AUTH_JWKS_URL: process.env.NEXT_PUBLIC_AUTH_JWKS_URL || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
    NEXT_PUBLIC_CUSTOMER_API_URL: process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'https://ng-customer-dev.dev1.ngnair.com',
    NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL: process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL || 'https://ng-customer-dev.dev1.ngnair.com/graphql',
    NEXT_PUBLIC_AUTH_DEBUG_MODE: process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE || 'false',
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Customer Management System',
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://ng-customer-admin-dev.dev1.ngnair.com',
    NEXT_PUBLIC_ADMIN_EMAIL: process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>',
  },
  images: {
    domains: process.env.NEXT_PUBLIC_IMAGE_DOMAINS ?
      process.env.NEXT_PUBLIC_IMAGE_DOMAINS.split(',') :
      ['localhost'],
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '10mb',
    },
  },
  output: 'standalone',
  // Disable ESLint and TypeScript checking during build for Docker
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;
