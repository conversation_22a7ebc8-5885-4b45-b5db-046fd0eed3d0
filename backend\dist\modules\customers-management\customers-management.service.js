"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CustomersManagementService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersManagementService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const customers_query_service_1 = require("../customers-query/customers-query.service");
let CustomersManagementService = CustomersManagementService_1 = class CustomersManagementService {
    prisma;
    queryService;
    logger = new common_1.Logger(CustomersManagementService_1.name);
    constructor(prisma, queryService) {
        this.prisma = prisma;
        this.queryService = queryService;
    }
    async create(data) {
        this.logger.log(`Creating customer: ${data.email}`);
        const existingCustomer = await this.prisma.customer.findUnique({
            where: { email: data.email },
        });
        if (existingCustomer) {
            throw new common_1.BadRequestException(`Customer with email ${data.email} already exists`);
        }
        const customer = await this.prisma.customer.create({
            data,
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
        });
        this.logger.log(`Customer created successfully: ${customer.id}`);
        return customer;
    }
    async update(id, data) {
        this.logger.log(`Updating customer: ${id}`);
        const existingCustomer = await this.queryService.findById(id);
        if (!existingCustomer) {
            throw new common_1.NotFoundException(`Customer not found: ${id}`);
        }
        const customer = await this.prisma.customer.update({
            where: { id },
            data,
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
        });
        this.logger.log(`Customer updated successfully: ${id}`);
        return customer;
    }
    async delete(id) {
        this.logger.log(`Soft deleting customer: ${id}`);
        const existingCustomer = await this.queryService.findById(id);
        if (!existingCustomer) {
            throw new common_1.NotFoundException(`Customer not found: ${id}`);
        }
        const customer = await this.prisma.customer.update({
            where: { id },
            data: { deletedAt: new Date() },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
        });
        this.logger.log(`Customer soft deleted successfully: ${id}`);
        return customer;
    }
    async updateStatus(id, status) {
        this.logger.log(`Updating customer status: ${id} to ${status}`);
        const existingCustomer = await this.queryService.findById(id);
        if (!existingCustomer) {
            throw new common_1.NotFoundException(`Customer not found: ${id}`);
        }
        const customer = await this.prisma.customer.update({
            where: { id },
            data: { status: status },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
        });
        this.logger.log(`Customer status updated successfully: ${id}`);
        return customer;
    }
    async verifyEmail(id) {
        this.logger.log(`Verifying email for customer: ${id}`);
        const existingCustomer = await this.queryService.findById(id);
        if (!existingCustomer) {
            throw new common_1.NotFoundException(`Customer not found: ${id}`);
        }
        const customer = await this.prisma.customer.update({
            where: { id },
            data: {
                isEmailVerified: true,
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
        });
        this.logger.log(`Email verified successfully for customer: ${id}`);
        return customer;
    }
    async verifyPhone(id) {
        this.logger.log(`Verifying phone for customer: ${id}`);
        const existingCustomer = await this.queryService.findById(id);
        if (!existingCustomer) {
            throw new common_1.NotFoundException(`Customer not found: ${id}`);
        }
        const customer = await this.prisma.customer.update({
            where: { id },
            data: {
                isPhoneVerified: true,
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
            },
        });
        this.logger.log(`Phone verified successfully for customer: ${id}`);
        return customer;
    }
    async bulkUpdate(ids, data) {
        this.logger.log(`Bulk updating ${ids.length} customers`);
        const result = await this.prisma.customer.updateMany({
            where: {
                id: { in: ids },
                deletedAt: null,
            },
            data,
        });
        this.logger.log(`Bulk update completed: ${result.count} customers updated`);
        return { count: result.count };
    }
    async bulkDelete(ids) {
        this.logger.log(`Bulk soft deleting ${ids.length} customers`);
        const result = await this.prisma.customer.updateMany({
            where: {
                id: { in: ids },
                deletedAt: null,
            },
            data: { deletedAt: new Date() },
        });
        this.logger.log(`Bulk delete completed: ${result.count} customers deleted`);
        return { count: result.count };
    }
    async addTag(id, tag) {
        this.logger.log(`Adding tag '${tag}' to customer: ${id}`);
        const existingCustomer = await this.queryService.findById(id);
        if (!existingCustomer) {
            throw new common_1.NotFoundException(`Customer not found: ${id}`);
        }
        const currentTags = existingCustomer.tags || [];
        if (currentTags.includes(tag)) {
            throw new common_1.BadRequestException(`Customer already has tag: ${tag}`);
        }
        const updatedTags = [...currentTags, tag];
        return this.update(id, { tags: updatedTags });
    }
    async removeTag(id, tag) {
        this.logger.log(`Removing tag '${tag}' from customer: ${id}`);
        const existingCustomer = await this.queryService.findById(id);
        if (!existingCustomer) {
            throw new common_1.NotFoundException(`Customer not found: ${id}`);
        }
        const currentTags = existingCustomer.tags || [];
        if (!currentTags.includes(tag)) {
            throw new common_1.BadRequestException(`Customer does not have tag: ${tag}`);
        }
        const updatedTags = currentTags.filter(t => t !== tag);
        return this.update(id, { tags: updatedTags });
    }
    async updateNotes(id, notes) {
        this.logger.log(`Updating notes for customer: ${id}`);
        return this.update(id, { notes });
    }
};
exports.CustomersManagementService = CustomersManagementService;
exports.CustomersManagementService = CustomersManagementService = CustomersManagementService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        customers_query_service_1.CustomersQueryService])
], CustomersManagementService);
//# sourceMappingURL=customers-management.service.js.map