import {
  Controller,
  Post,
  Put,
  Param,
  Body,
  Logger,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { CustomersAdminService, CustomerWithRelations } from './customers-admin.service';
import { CustomerResponseDto } from '../customers-shared/dto/customer-shared.dto';

@ApiTags('customers-admin')
@Controller('customers')
export class CustomersAdminController {
  private readonly logger = new Logger(CustomersAdminController.name);

  constructor(private readonly customersAdminService: CustomersAdminService) {}

  /**
   * Convert CustomerWithRelations to CustomerResponseDto
   */
  private mapToResponseDto(customer: CustomerWithRelations): CustomerResponseDto {
    return {
      ...customer,
      externalId: customer.externalId || undefined,
    } as CustomerResponseDto;
  }

  @Post(':id/suspend')
  @ApiOperation({ 
    summary: 'Suspend customer account (Admin only)',
    description: 'Suspend a customer account, preventing them from accessing services. This is an administrative action that requires elevated permissions.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer suspended successfully',
    type: CustomerResponseDto,
    examples: {
      success: {
        summary: 'Successful customer suspension',
        value: {
          id: 'cmca6q7w40000l9015jo4lc5o',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          status: 'SUSPENDED',
          suspendedAt: '2024-01-15T16:00:00Z',
          updatedAt: '2024-01-15T16:00:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions to suspend customer',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Customer already suspended or not eligible for suspension',
  })
  async suspendCustomer(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Suspending customer ${id}`);

    const customer = await this.customersAdminService.suspendCustomer(id);
    return this.mapToResponseDto(customer);
  }

  @Post(':id/activate')
  @ApiOperation({ 
    summary: 'Activate customer account (Admin only)',
    description: 'Activate a suspended or inactive customer account, restoring their access to services. This is an administrative action that requires elevated permissions.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer activated successfully',
    type: CustomerResponseDto,
    examples: {
      success: {
        summary: 'Successful customer activation',
        value: {
          id: 'cmca6q7w40000l9015jo4lc5o',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          status: 'ACTIVE',
          activatedAt: '2024-01-15T16:30:00Z',
          updatedAt: '2024-01-15T16:30:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions to activate customer',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Customer already active or not eligible for activation',
  })
  async activateCustomer(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Activating customer ${id}`);

    const customer = await this.customersAdminService.activateCustomer(id);
    return this.mapToResponseDto(customer);
  }

  @Put(':id/admin-update')
  @ApiOperation({ 
    summary: 'Admin update customer (Admin only)',
    description: 'Perform administrative updates on customer records with elevated permissions. This allows updating sensitive fields that regular updates cannot modify.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer updated successfully by admin',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions for admin update',
  })
  async adminUpdateCustomer(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateData: {
      status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION' | 'BLOCKED';
      isEmailVerified?: boolean;
      isPhoneVerified?: boolean;
      isKycVerified?: boolean;
      tags?: string[];
      notes?: string;
      externalId?: string;
    },
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Admin updating customer ${id}`);

    const customer = await this.customersAdminService.adminUpdateCustomer(id, updateData);
    return this.mapToResponseDto(customer);
  }

  @Post(':id/block')
  @ApiOperation({ 
    summary: 'Block customer account (Admin only)',
    description: 'Block a customer account for security or compliance reasons. This is a more severe action than suspension and requires elevated permissions.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer blocked successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions to block customer',
  })
  async blockCustomer(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() blockData: {
      reason?: string;
      notes?: string;
    },
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Blocking customer ${id}`);

    const customer = await this.customersAdminService.blockCustomer(id);
    return this.mapToResponseDto(customer);
  }
}
