"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentUser = exports.Public = exports.Permissions = exports.Roles = exports.AuthSharedGuard = exports.AuthModuleStandalone = exports.AuthSharedModule = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_module_1 = require("../../config/config.module");
const auth_service_1 = require("./auth.service");
const auth_guard_1 = require("./auth.guard");
const auth_middleware_1 = require("./auth.middleware");
const auth_controller_1 = require("./auth.controller");
const guards_1 = require("./guards");
let AuthSharedModule = class AuthSharedModule {
    configure(consumer) {
        consumer
            .apply(auth_middleware_1.AuthSharedMiddleware)
            .forRoutes('*');
    }
};
exports.AuthSharedModule = AuthSharedModule;
exports.AuthSharedModule = AuthSharedModule = __decorate([
    (0, common_1.Module)({
        imports: [config_module_1.ConfigModule, axios_1.HttpModule],
        providers: [auth_service_1.AuthSharedService, auth_guard_1.AuthSharedGuard, auth_middleware_1.AuthSharedMiddleware, guards_1.ApiGuard, guards_1.AdminGuard],
        controllers: [auth_controller_1.AuthSharedController],
        exports: [auth_service_1.AuthSharedService, auth_guard_1.AuthSharedGuard, auth_middleware_1.AuthSharedMiddleware, guards_1.ApiGuard, guards_1.AdminGuard],
    })
], AuthSharedModule);
class AuthModuleStandalone {
    authService;
    authGuard;
    constructor(config) {
        throw new Error('Standalone usage not supported with HttpService dependency. Use AuthSharedModule instead.');
    }
    getAuthService() {
        return this.authService;
    }
    getAuthGuard() {
        return this.authGuard;
    }
}
exports.AuthModuleStandalone = AuthModuleStandalone;
__exportStar(require("./auth.service"), exports);
var auth_guard_2 = require("./auth.guard");
Object.defineProperty(exports, "AuthSharedGuard", { enumerable: true, get: function () { return auth_guard_2.AuthSharedGuard; } });
Object.defineProperty(exports, "Roles", { enumerable: true, get: function () { return auth_guard_2.Roles; } });
Object.defineProperty(exports, "Permissions", { enumerable: true, get: function () { return auth_guard_2.Permissions; } });
Object.defineProperty(exports, "Public", { enumerable: true, get: function () { return auth_guard_2.Public; } });
__exportStar(require("./auth.middleware"), exports);
__exportStar(require("./auth.controller"), exports);
__exportStar(require("./guards"), exports);
var user_decorator_1 = require("./decorators/user.decorator");
Object.defineProperty(exports, "getCurrentUser", { enumerable: true, get: function () { return user_decorator_1.getCurrentUser; } });
__exportStar(require("./types/auth.types"), exports);
__exportStar(require("./types"), exports);
//# sourceMappingURL=auth-shared.module.js.map