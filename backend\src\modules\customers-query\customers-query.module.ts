import { Module } from '@nestjs/common';
import { CustomersQueryService } from './customers-query.service';
import { CustomersQueryController } from './customers-query.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '../../config/config.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
  ],
  controllers: [
    CustomersQueryController,
  ],
  providers: [
    CustomersQueryService,
  ],
  exports: [
    CustomersQueryService,
  ],
})
export class CustomersQueryModule {}
