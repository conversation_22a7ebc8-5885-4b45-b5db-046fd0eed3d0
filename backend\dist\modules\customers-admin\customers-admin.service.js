"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CustomersAdminService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersAdminService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let CustomersAdminService = CustomersAdminService_1 = class CustomersAdminService {
    prisma;
    logger = new common_1.Logger(CustomersAdminService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async suspendCustomer(customerId) {
        this.logger.log(`Suspending customer ${customerId}`);
        const customer = await this.prisma.customer.update({
            where: { id: customerId },
            data: {
                status: client_1.CustomerStatus.SUSPENDED,
                updatedAt: new Date(),
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        await this.prisma.auditLog.create({
            data: {
                customerId,
                action: client_1.AuditAction.STATUS_CHANGE,
                entity: 'customer',
                entityId: customerId,
                description: `Customer suspended by admin`,
                metadata: { suspendedAt: new Date(), reason: 'Administrative action' },
            },
        });
        return customer;
    }
    async activateCustomer(customerId) {
        this.logger.log(`Activating customer ${customerId}`);
        const customer = await this.prisma.customer.update({
            where: { id: customerId },
            data: {
                status: client_1.CustomerStatus.ACTIVE,
                updatedAt: new Date(),
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        await this.prisma.auditLog.create({
            data: {
                customerId,
                action: client_1.AuditAction.STATUS_CHANGE,
                entity: 'customer',
                entityId: customerId,
                description: `Customer activated by admin`,
                metadata: { activatedAt: new Date() },
            },
        });
        return customer;
    }
    async blockCustomer(customerId) {
        this.logger.log(`Blocking customer ${customerId}`);
        const customer = await this.prisma.customer.update({
            where: { id: customerId },
            data: {
                status: client_1.CustomerStatus.BLOCKED,
                updatedAt: new Date(),
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        await this.prisma.auditLog.create({
            data: {
                customerId,
                action: client_1.AuditAction.STATUS_CHANGE,
                entity: 'customer',
                entityId: customerId,
                description: `Customer blocked by admin`,
                metadata: { blockedAt: new Date(), reason: 'Administrative action' },
            },
        });
        return customer;
    }
    async adminUpdateCustomer(customerId, updateData) {
        this.logger.log(`Admin updating customer ${customerId}`);
        const customer = await this.prisma.customer.update({
            where: { id: customerId },
            data: {
                ...updateData,
                updatedAt: new Date(),
            },
            include: {
                addresses: true,
                contacts: true,
                preferences: true,
                auditLogs: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
        await this.prisma.auditLog.create({
            data: {
                customerId,
                action: client_1.AuditAction.UPDATE,
                entity: 'customer',
                entityId: customerId,
                description: `Customer updated by admin`,
                metadata: {
                    updatedFields: Object.keys(updateData),
                    updatedAt: new Date(),
                },
            },
        });
        return customer;
    }
};
exports.CustomersAdminService = CustomersAdminService;
exports.CustomersAdminService = CustomersAdminService = CustomersAdminService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CustomersAdminService);
//# sourceMappingURL=customers-admin.service.js.map